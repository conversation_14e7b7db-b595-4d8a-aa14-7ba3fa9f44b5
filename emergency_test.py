#!/usr/bin/env python3
"""
EMERGENCY TEST: Use only small models to prevent system freezing.
This test uses tiny models that won't cause memory issues.
"""

import sys
import os
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def emergency_mcq_test():
    """Test MCQ generation with emergency CPU-only mode."""
    try:
        logger.info("🚨 EMERGENCY TEST: Testing with CPU-only small models")
        logger.info("📋 This will use DialoGPT-medium (much smaller than 7B models)")
        
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        # Create generator
        generator = OfflineMCQGenerator()
        
        # Test initialization
        logger.info("🔄 Initializing with emergency CPU-only strategy...")
        success = generator.initialize()
        
        if success:
            logger.info("✅ Emergency initialization successful!")
            
            # Test simple content
            test_content = "Python is a programming language."
            
            logger.info("🧠 Testing MCQ generation with small content...")
            result = generator.generate_question_sync(test_content, difficulty="easy")
            
            if result and 'question' in result:
                logger.info("✅ Emergency MCQ generation WORKS!")
                logger.info(f"📝 Question: {result['question']}")
                return True
            else:
                logger.warning("⚠️ MCQ generation returned empty result")
                return False
                
        else:
            logger.error("❌ Emergency initialization failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Emergency test failed: {e}")
        return False

def main():
    """Run emergency test."""
    logger.info("🚨 EMERGENCY CRASH PREVENTION TEST")
    logger.info("=" * 50)
    logger.info("📋 Using ONLY small models to prevent freezing")
    logger.info("📋 DialoGPT-medium instead of 7B models")
    logger.info("📋 CPU-only mode for maximum stability")
    logger.info("")
    
    success = emergency_mcq_test()
    
    logger.info("")
    logger.info("=" * 50)
    logger.info("🏁 EMERGENCY TEST RESULTS")
    logger.info("=" * 50)
    
    if success:
        logger.info("✅ EMERGENCY MODE WORKS!")
        logger.info("💡 Your system can generate MCQs without freezing")
        logger.info("💡 Using smaller models on CPU for stability")
        logger.info("💡 This is slower but prevents crashes completely")
    else:
        logger.error("❌ EMERGENCY MODE FAILED")
        logger.error("💡 Even small models are causing issues")
        logger.error("💡 May need to check system resources")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
