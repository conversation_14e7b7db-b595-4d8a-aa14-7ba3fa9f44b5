#!/usr/bin/env python3
"""
Test GGUF Integration

This script tests the GGUF engine integration without requiring actual model files.
It verifies that the code structure is correct and handles missing dependencies gracefully.
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_gguf_model_inference_import():
    """Test if GGUFModelInference can be imported."""
    try:
        from knowledge_app.core.gguf_model_inference import GGUFModelInference
        logger.info("✅ GGUFModelInference import successful")
        return True
    except ImportError as e:
        logger.error(f"❌ GGUFModelInference import failed: {e}")
        return False

def test_gguf_model_inference_creation():
    """Test if GGUFModelInference can be created."""
    try:
        from knowledge_app.core.gguf_model_inference import GGUFModelInference
        
        # Create instance with dummy path
        engine = GGUFModelInference("dummy_model.gguf")
        logger.info("✅ GGUFModelInference creation successful")
        
        # Test configuration
        info = engine.get_model_info()
        logger.info(f"✅ Model info: {info}")
        
        return True
    except Exception as e:
        logger.error(f"❌ GGUFModelInference creation failed: {e}")
        return False

def test_offline_mcq_generator_import():
    """Test if OfflineMCQGenerator can be imported with GGUF support."""
    try:
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        logger.info("✅ OfflineMCQGenerator import successful")
        return True
    except ImportError as e:
        logger.error(f"❌ OfflineMCQGenerator import failed: {e}")
        return False

def test_offline_mcq_generator_creation():
    """Test if OfflineMCQGenerator can be created with GGUF support."""
    try:
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator

        # Create instance
        generator = OfflineMCQGenerator()
        logger.info("✅ OfflineMCQGenerator creation successful")

        # Check GGUF model path
        logger.info(f"✅ GGUF model path: {generator.gguf_model_path}")

        return True
    except Exception as e:
        logger.error(f"❌ OfflineMCQGenerator creation failed: {e}")
        return False

def test_gguf_model_detection():
    """Test GGUF model detection logic."""
    try:
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator

        generator = OfflineMCQGenerator()

        # Test model path existence (should return False since no models exist)
        model_exists = os.path.exists(generator.gguf_model_path)
        if not model_exists:
            logger.info("✅ GGUF model detection working (no models found as expected)")
        else:
            logger.info(f"✅ GGUF model found: {generator.gguf_model_path}")

        return True
    except Exception as e:
        logger.error(f"❌ GGUF model detection failed: {e}")
        return False

def test_initialization_logic():
    """Test the initialization logic without actually loading models."""
    try:
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator

        generator = OfflineMCQGenerator()

        # Test GGUF initialization (should fail gracefully since no model file exists)
        gguf_success = generator.initialize()
        logger.info(f"✅ GGUF initialization test: {gguf_success} (expected False)")

        return True
    except Exception as e:
        logger.error(f"❌ Initialization logic test failed: {e}")
        return False

def test_dependency_handling():
    """Test how the code handles missing llama-cpp-python dependency."""
    try:
        from knowledge_app.core.gguf_model_inference import GGUFModelInference
        
        engine = GGUFModelInference("dummy_model.gguf")
        
        # Test dependency check
        has_deps = engine._check_dependencies()
        logger.info(f"✅ Dependency check: {has_deps}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Dependency handling test failed: {e}")
        return False

def test_config_files():
    """Test if configuration files are properly created."""
    config_files = [
        "config/gguf_config.json",
        "docs/gguf_setup_guide.md",
        "install_gguf_engine.py"
    ]
    
    all_exist = True
    for config_file in config_files:
        if os.path.exists(config_file):
            logger.info(f"✅ Config file exists: {config_file}")
        else:
            logger.error(f"❌ Config file missing: {config_file}")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests."""
    logger.info("🧪 Testing GGUF Integration")
    logger.info("=" * 50)
    
    tests = [
        ("GGUF Model Inference Import", test_gguf_model_inference_import),
        ("GGUF Model Inference Creation", test_gguf_model_inference_creation),
        ("Offline MCQ Generator Import", test_offline_mcq_generator_import),
        ("Offline MCQ Generator Creation", test_offline_mcq_generator_creation),
        ("GGUF Model Detection", test_gguf_model_detection),
        ("Initialization Logic", test_initialization_logic),
        ("Dependency Handling", test_dependency_handling),
        ("Configuration Files", test_config_files),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Testing: {test_name}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
    
    logger.info(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! GGUF integration is ready.")
        logger.info("\n📋 Next Steps:")
        logger.info("1. Run: python install_gguf_engine.py")
        logger.info("2. Download a GGUF model")
        logger.info("3. Place it in models/gguf_models/")
        logger.info("4. Restart the application")
    else:
        logger.error("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
