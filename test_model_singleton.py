#!/usr/bin/env python3
"""
Test script for Model Singleton to verify it prevents model reloading

This script tests that the model is loaded only once and reused for subsequent generations.
"""

import asyncio
import logging
import sys
import os
import time

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_model_singleton():
    """Test that the model singleton prevents reloading"""
    print("\n" + "="*60)
    print("🚀 TESTING MODEL SINGLETON - NO MORE RELOADING!")
    print("="*60)
    
    try:
        from knowledge_app.core.model_singleton import get_model_singleton, get_model
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        # Test 1: First initialization
        print("\n📝 Test 1: First model initialization")
        start_time = time.time()
        
        generator1 = OfflineMCQGenerator()
        success1 = generator1.initialize()
        
        first_load_time = time.time() - start_time
        print(f"✅ First initialization: {success1} (took {first_load_time:.2f} seconds)")
        
        if not success1:
            print("❌ First initialization failed - cannot continue test")
            return False
        
        # Test 2: Second initialization (should be instant!)
        print("\n📝 Test 2: Second model initialization (should be instant!)")
        start_time = time.time()
        
        generator2 = OfflineMCQGenerator()
        success2 = generator2.initialize()
        
        second_load_time = time.time() - start_time
        print(f"✅ Second initialization: {success2} (took {second_load_time:.2f} seconds)")
        
        # Test 3: Third initialization (should also be instant!)
        print("\n📝 Test 3: Third model initialization (should also be instant!)")
        start_time = time.time()
        
        generator3 = OfflineMCQGenerator()
        success3 = generator3.initialize()
        
        third_load_time = time.time() - start_time
        print(f"✅ Third initialization: {success3} (took {third_load_time:.2f} seconds)")
        
        # Analyze results
        print("\n" + "="*60)
        print("📊 PERFORMANCE ANALYSIS")
        print("="*60)
        
        print(f"First load time:  {first_load_time:.2f} seconds")
        print(f"Second load time: {second_load_time:.2f} seconds")
        print(f"Third load time:  {third_load_time:.2f} seconds")
        
        # Check if subsequent loads are significantly faster
        if second_load_time < 2.0 and third_load_time < 2.0:
            print("🎉 SUCCESS! Subsequent initializations are instant!")
            print("🛡️ Model singleton is working - no more reloading!")
            
            # Test actual generation to make sure it works
            print("\n📝 Test 4: Quick MCQ generation test")
            test_context = "Magnetism is a physical phenomenon produced by the motion of electric charge."
            
            start_time = time.time()
            result = await generator3.generate_quiz_async(test_context, "medium")
            generation_time = time.time() - start_time
            
            if result and result.get('question'):
                print(f"✅ MCQ generation successful in {generation_time:.2f} seconds")
                print(f"Question: {result['question'][:100]}...")
                return True
            else:
                print("⚠️ MCQ generation failed, but singleton is working")
                return True  # Singleton still works even if generation fails
        else:
            print("❌ FAILED! Subsequent initializations are still slow")
            print("❌ Model singleton is not working properly")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

async def test_singleton_info():
    """Test singleton info methods"""
    print("\n" + "="*60)
    print("📊 TESTING SINGLETON INFO METHODS")
    print("="*60)
    
    try:
        from knowledge_app.core.model_singleton import get_model_singleton, get_global_model_info
        
        singleton = get_model_singleton()
        info = get_global_model_info()
        
        print(f"Model status: {info.get('status', 'unknown')}")
        print(f"Current model ID: {info.get('current_model_id', 'none')}")
        print(f"Is loading: {info.get('is_loading', 'unknown')}")
        
        if info.get('status') == 'loaded':
            print("✅ Singleton reports model is loaded")
            return True
        else:
            print("⚠️ Singleton reports model is not loaded")
            return False
            
    except Exception as e:
        print(f"❌ Singleton info test failed: {e}")
        return False

async def main():
    """Run all singleton tests"""
    print("🚀 STARTING MODEL SINGLETON TESTING")
    print("This will verify that your model reloading issue is FIXED!")
    
    # Run tests
    test_results = []
    
    test_results.append(await test_model_singleton())
    test_results.append(await test_singleton_info())
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("🛡️ Model singleton is working perfectly!")
        print("🚀 Your app will no longer reload models unnecessarily!")
        print("⚡ MCQ generation should be much faster now!")
    else:
        print("⚠️ Some tests failed - singleton may need adjustment")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
