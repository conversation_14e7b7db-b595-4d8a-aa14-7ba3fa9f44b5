#!/usr/bin/env python3
"""
Test script to verify that the prompt leakage issue is fixed
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_pure_content_separation():
    """Test that content and instructions are properly separated"""
    print("🧪 Testing Pure Content Separation")
    print("-" * 50)
    
    # Test the Inquisitor's Mandate prompt creation
    try:
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        generator = OfflineMCQGenerator()
        
        # Test pure content (what should be in CONTEXT)
        pure_content = """
        Magnetism is a physical phenomenon produced by the motion of electric charge, 
        resulting in attractive and repulsive forces between objects. Magnetic fields 
        are created by moving electric charges and intrinsic magnetic moments of 
        elementary particles. The Lorentz force describes the force on a charged 
        particle moving through electric and magnetic fields: F = q(E + v × B).
        """
        
        # Create prompt using pure content method
        prompt = generator._create_inquisitor_prompt_pure_content(
            pure_content, "Magnetism", "medium", "understanding"
        )
        
        # Verify the prompt structure
        if "### CONTEXT ###" in prompt:
            context_start = prompt.find("### CONTEXT ###") + len("### CONTEXT ###")
            context_end = prompt.find("### TASK & CONSTRAINTS ###")

            if context_end > context_start:
                context_section = prompt[context_start:context_end].strip()

                # Debug: Print the actual context section
                print(f"📝 Context section content: {context_section[:100]}...")

                # Check that context contains factual content, not instructions
                if "Magnetism is a physical phenomenon" in context_section:
                    print("✅ Context section contains factual content about magnetism")
                else:
                    print("❌ Context section missing expected factual content")
                    return False

                # Check that context does NOT contain instruction words
                # Only check for words that would indicate instruction leakage
                instruction_words = ["generate", "suitable for", "mode learning", "difficulty level"]
                found_instructions = [word for word in instruction_words if word.lower() in context_section.lower()]

                if not found_instructions:
                    print("✅ Context section is free of instruction language")
                else:
                    print(f"❌ Context section contains instruction words: {found_instructions}")
                    return False
                
                # Check that instructions are in the proper section
                if "### TASK & CONSTRAINTS ###" in prompt:
                    print("✅ Instructions are properly separated in TASK section")
                else:
                    print("❌ Missing TASK & CONSTRAINTS section")
                    return False
                
                print("✅ Prompt structure is correct - content and instructions separated!")
                return True
            else:
                print("❌ Could not find proper context section boundaries")
                return False
        else:
            print("❌ Prompt missing CONTEXT section")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_mcq_manager_content_retrieval():
    """Test that MCQ manager retrieves pure content"""
    print("\n🧪 Testing MCQ Manager Content Retrieval")
    print("-" * 50)
    
    try:
        from knowledge_app.core.mcq_manager import MCQManager
        
        # Create a mock MCQ manager
        class MockMCQManager(MCQManager):
            def __init__(self):
                # Skip full initialization for testing
                self._use_post_processing = False
                self.post_processor = None
                
            def _get_curated_fallback_content(self, topic: str) -> str:
                # Test the curated content method
                return super()._get_curated_fallback_content(topic)
        
        manager = MockMCQManager()
        
        # Test curated content retrieval
        magnetism_content = manager._get_curated_fallback_content("magnetism")
        
        # Verify content is factual, not instructional
        if "Magnetism is a physical phenomenon" in magnetism_content:
            print("✅ Curated content contains factual information")
        else:
            print("❌ Curated content missing expected factual information")
            return False
        
        # Verify content doesn't contain instruction language
        instruction_phrases = [
            "generate a question",
            "create a question", 
            "suitable for learning",
            "difficulty level"
        ]
        
        found_instructions = [phrase for phrase in instruction_phrases 
                            if phrase.lower() in magnetism_content.lower()]
        
        if not found_instructions:
            print("✅ Curated content is free of instruction language")
        else:
            print(f"❌ Curated content contains instruction phrases: {found_instructions}")
            return False
        
        print("✅ MCQ Manager content retrieval working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_quiz_params_structure():
    """Test that quiz parameters are structured correctly"""
    print("\n🧪 Testing Quiz Parameters Structure")
    print("-" * 50)
    
    try:
        # Test the new quiz parameters structure
        quiz_params = {
            'topic': 'Magnetism',
            'difficulty': 'medium',
            'mode': 'Casual',
            'question_type': 'Multiple Choice',
            'cognitive_level': 'understanding'
        }
        
        # Verify structure
        required_keys = ['topic', 'difficulty', 'mode', 'question_type', 'cognitive_level']
        missing_keys = [key for key in required_keys if key not in quiz_params]
        
        if not missing_keys:
            print("✅ Quiz parameters contain all required keys")
        else:
            print(f"❌ Quiz parameters missing keys: {missing_keys}")
            return False
        
        # Verify no instruction language in topic
        topic = quiz_params['topic']
        if not any(word in topic.lower() for word in ['generate', 'create', 'question', 'suitable']):
            print("✅ Topic parameter contains only subject matter, no instructions")
        else:
            print("❌ Topic parameter contains instruction language")
            return False
        
        print("✅ Quiz parameters structure is correct!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_cognitive_level_mapping():
    """Test cognitive level mapping functionality"""
    print("\n🧪 Testing Cognitive Level Mapping")
    print("-" * 50)
    
    try:
        from knowledge_app.core.advanced_rag_mcq_generator import AdvancedRAGMCQGenerator
        
        generator = AdvancedRAGMCQGenerator()
        
        # Test cognitive level string to enum conversion
        test_cases = [
            ("understanding", "UNDERSTANDING"),
            ("applying", "APPLYING"),
            ("analyzing", "ANALYZING"),
            ("evaluating", "EVALUATING"),
            ("invalid", "UNDERSTANDING")  # Should default to understanding
        ]
        
        for input_level, expected_enum_name in test_cases:
            result = generator._string_to_cognitive_level(input_level)
            if result.name == expected_enum_name:
                print(f"✅ {input_level} -> {result.name}")
            else:
                print(f"❌ {input_level} -> {result.name} (expected {expected_enum_name})")
                return False
        
        print("✅ Cognitive level mapping working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run all tests to verify prompt leakage fix"""
    print("🔬 Testing Prompt Leakage Fix")
    print("=" * 60)
    
    tests = [
        test_pure_content_separation,
        test_mcq_manager_content_retrieval,
        test_quiz_params_structure,
        test_cognitive_level_mapping
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Prompt leakage issue is FIXED!")
        print("\n📋 Summary of fixes:")
        print("   ✅ Pure content separation implemented")
        print("   ✅ Instruction language removed from context")
        print("   ✅ MCQ manager uses factual content retrieval")
        print("   ✅ Quiz parameters properly structured")
        print("   ✅ Cognitive level mapping working")
        print("\n🚀 The AI will now generate questions about the actual topic,")
        print("   not about the instructions to generate questions!")
        return True
    else:
        print(f"❌ {total_tests - passed_tests} tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
