from PyQt5.QtGui import QFont, QPixmap, QPainter, QFontMetrics, QImage, QColor
from PyQt5.QtCore import Qt
import re
import logging

logger = logging.getLogger(__name__)

def render_latex_to_pixmap(latex, font_size=12, dpi=100, max_width=800, background_color=None):
    """
    Enhanced LaTeX renderer with better math support and formatting

    Args:
        latex: LaTeX string to render
        font_size: Base font size
        dpi: Dots per inch for rendering
        max_width: Maximum width of the rendered image
        background_color: Background color (None for transparent)

    Returns:
        QPixmap containing the rendered LaTeX
    """
    try:
        pixmap = QPixmap(max_width, 1000)
        if background_color:
            pixmap.fill(QColor(background_color))
        else:
            pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing, True)
        painter.setRenderHint(QPainter.TextAntialiasing, True)

        # Enhanced font setup
        normal_font = QFont("Times New Roman", font_size)
        math_font = QFont("Times New Roman", font_size, QFont.Bold)
        italic_font = QFont("Times New Roman", font_size)
        italic_font.setItalic(True)

        painter.setFont(normal_font)

        # Enhanced parsing for LaTeX constructs
        parts = _parse_latex_enhanced(latex)

        y = 20
        max_width_used = 0
        line_height = QFontMetrics(normal_font).height() + 10

        for text, text_type in parts:
            if not text.strip():
                continue

            lines = text.split('\n')
            for line in lines:
                if not line.strip():
                    y += line_height // 2
                    continue

                # Choose font based on text type
                if text_type == 'math':
                    font = math_font
                elif text_type == 'italic':
                    font = italic_font
                else:
                    font = normal_font

                painter.setFont(font)
                metrics = painter.fontMetrics()

                # Process mathematical symbols
                if text_type == 'math':
                    line = _process_math_symbols(line)

                text_width = metrics.horizontalAdvance(line)

                # Handle text wrapping
                if text_width > max_width - 40:
                    words = line.split()
                    current_line = ""
                    for word in words:
                        test_line = current_line + " " + word if current_line else word
                        if metrics.horizontalAdvance(test_line) <= max_width - 40:
                            current_line = test_line
                        else:
                            if current_line:
                                x = (max_width - metrics.horizontalAdvance(current_line)) // 2
                                painter.drawText(x, y, current_line)
                                max_width_used = max(max_width_used, metrics.horizontalAdvance(current_line))
                                y += line_height
                            current_line = word
                    if current_line:
                        x = (max_width - metrics.horizontalAdvance(current_line)) // 2
                        painter.drawText(x, y, current_line)
                        max_width_used = max(max_width_used, metrics.horizontalAdvance(current_line))
                        y += line_height
                else:
                    x = (max_width - text_width) // 2
                    painter.drawText(x, y, line)
                    max_width_used = max(max_width_used, text_width)
                    y += line_height

        painter.end()
        return pixmap.copy(0, 0, max(max_width_used + 40, max_width), y + 20)

    except Exception as e:
        logger.error(f"LaTeX rendering failed: {e}")
        # Return a simple error pixmap
        error_pixmap = QPixmap(400, 100)
        error_pixmap.fill(Qt.white)
        error_painter = QPainter(error_pixmap)
        error_painter.drawText(10, 50, "LaTeX rendering error")
        error_painter.end()
        return error_pixmap


def _parse_latex_enhanced(latex):
    """Enhanced LaTeX parsing with support for more constructs"""
    parts = []
    current_text = ""
    i = 0

    while i < len(latex):
        # Handle display math $$...$$
        if latex[i:i+2] == "$$":
            if current_text:
                parts.append((current_text, 'normal'))
                current_text = ""
            i += 2
            math_text = ""
            while i < len(latex) and latex[i:i+2] != "$$":
                math_text += latex[i]
                i += 1
            if i < len(latex):
                i += 2
            parts.append((math_text, 'math'))

        # Handle inline math $...$
        elif latex[i] == "$" and (i == 0 or latex[i-1] != "$") and (i+1 >= len(latex) or latex[i+1] != "$"):
            if current_text:
                parts.append((current_text, 'normal'))
                current_text = ""
            i += 1
            math_text = ""
            while i < len(latex) and latex[i] != "$":
                math_text += latex[i]
                i += 1
            if i < len(latex):
                i += 1
            parts.append((math_text, 'math'))

        # Handle italic text \textit{...}
        elif latex[i:i+8] == "\\textit{":
            if current_text:
                parts.append((current_text, 'normal'))
                current_text = ""
            i += 8
            italic_text = ""
            brace_count = 1
            while i < len(latex) and brace_count > 0:
                if latex[i] == '{':
                    brace_count += 1
                elif latex[i] == '}':
                    brace_count -= 1
                if brace_count > 0:
                    italic_text += latex[i]
                i += 1
            parts.append((italic_text, 'italic'))

        else:
            current_text += latex[i]
            i += 1

    if current_text:
        parts.append((current_text, 'normal'))

    return parts


def _process_math_symbols(text):
    """Convert common LaTeX math symbols to Unicode equivalents"""
    symbol_map = {
        '\\alpha': 'α', '\\beta': 'β', '\\gamma': 'γ', '\\delta': 'δ',
        '\\epsilon': 'ε', '\\theta': 'θ', '\\lambda': 'λ', '\\mu': 'μ',
        '\\pi': 'π', '\\sigma': 'σ', '\\tau': 'τ', '\\phi': 'φ',
        '\\omega': 'ω', '\\Delta': 'Δ', '\\Theta': 'Θ', '\\Lambda': 'Λ',
        '\\Pi': 'Π', '\\Sigma': 'Σ', '\\Phi': 'Φ', '\\Omega': 'Ω',
        '\\infty': '∞', '\\pm': '±', '\\mp': '∓', '\\times': '×',
        '\\div': '÷', '\\neq': '≠', '\\leq': '≤', '\\geq': '≥',
        '\\approx': '≈', '\\equiv': '≡', '\\subset': '⊂', '\\supset': '⊃',
        '\\in': '∈', '\\notin': '∉', '\\cup': '∪', '\\cap': '∩',
        '\\int': '∫', '\\sum': '∑', '\\prod': '∏', '\\sqrt': '√',
        '\\partial': '∂', '\\nabla': '∇', '\\rightarrow': '→',
        '\\leftarrow': '←', '\\leftrightarrow': '↔', '\\Rightarrow': '⇒',
        '\\Leftarrow': '⇐', '\\Leftrightarrow': '⇔'
    }

    for latex_symbol, unicode_symbol in symbol_map.items():
        text = text.replace(latex_symbol, unicode_symbol)

    # Handle fractions \frac{a}{b}
    text = re.sub(r'\\frac\{([^}]+)\}\{([^}]+)\}', r'(\1)/(\2)', text)

    # Handle superscripts ^{...} or ^x
    text = re.sub(r'\^{([^}]+)}', r'^\1', text)
    text = re.sub(r'\^([a-zA-Z0-9])', r'^\1', text)

    # Handle subscripts _{...} or _x
    text = re.sub(r'_{([^}]+)}', r'_\1', text)
    text = re.sub(r'_([a-zA-Z0-9])', r'_\1', text)

    return text

def validate_latex(latex_code):
    """Enhanced LaTeX validation with better error checking"""
    try:
        # Check for balanced delimiters
        dollar_stack = []
        double_dollar_stack = []
        brace_stack = []

        i = 0
        while i < len(latex_code):
            # Check for $$
            if latex_code[i:i+2] == '$$':
                if not double_dollar_stack:
                    double_dollar_stack.append('$$')
                else:
                    double_dollar_stack.pop()
                i += 2
            # Check for single $
            elif latex_code[i] == '$':
                if not dollar_stack:
                    dollar_stack.append('$')
                else:
                    dollar_stack.pop()
                i += 1
            # Check for braces
            elif latex_code[i] == '{':
                brace_stack.append('{')
                i += 1
            elif latex_code[i] == '}':
                if not brace_stack:
                    raise ValueError("Unmatched closing brace '}' in LaTeX.")
                brace_stack.pop()
                i += 1
            else:
                i += 1

        # Check for unbalanced delimiters
        if double_dollar_stack:
            raise ValueError("Unbalanced $$ in LaTeX.")
        if dollar_stack:
            raise ValueError("Unbalanced $ in LaTeX.")
        if brace_stack:
            raise ValueError("Unbalanced braces {} in LaTeX.")

        return latex_code

    except Exception as e:
        logger.error(f"LaTeX validation failed: {e}")
        raise


def detect_latex_in_text(text):
    """Detect if text contains LaTeX markup"""
    latex_patterns = [
        r'\$\$.*?\$\$',  # Display math
        r'\$.*?\$',      # Inline math
        r'\\[a-zA-Z]+',  # LaTeX commands
        r'\\textit\{.*?\}',  # Italic text
        r'\\frac\{.*?\}\{.*?\}',  # Fractions
    ]

    for pattern in latex_patterns:
        if re.search(pattern, text):
            return True
    return False


def render_text_with_latex(text, font_size=12, max_width=800, background_color=None):
    """
    Render text that may contain LaTeX markup

    Args:
        text: Text that may contain LaTeX
        font_size: Font size for rendering
        max_width: Maximum width
        background_color: Background color

    Returns:
        QPixmap with rendered text
    """
    if detect_latex_in_text(text):
        return render_latex_to_pixmap(text, font_size, max_width=max_width, background_color=background_color)
    else:
        # Render as plain text
        pixmap = QPixmap(max_width, 200)
        if background_color:
            pixmap.fill(QColor(background_color))
        else:
            pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing, True)
        painter.setRenderHint(QPainter.TextAntialiasing, True)

        font = QFont("Times New Roman", font_size)
        painter.setFont(font)

        # Simple text rendering with word wrap
        rect = painter.boundingRect(0, 0, max_width - 40, 0, Qt.TextWordWrap, text)
        painter.drawText(20, 20, max_width - 40, rect.height(), Qt.TextWordWrap, text)

        painter.end()
        return pixmap.copy(0, 0, max_width, rect.height() + 40)
