"""
GGUF Model Inference Engine

This module provides a stable, crash-resistant alternative to the transformers library
for running large language models on consumer hardware like RTX 3060 12GB.

GGUF (GPT-Generated Unified Format) models are pre-quantized and optimized for
efficient inference with minimal VRAM usage and maximum stability.

Key Benefits:
- No VRAM spikes during loading (prevents system crashes)
- Highly optimized for consumer GPUs
- Stable on Windows systems
- Lower memory footprint than transformers + bitsandbytes
- Direct CUDA integration without driver conflicts

Usage:
    engine = GGUFModelInference("models/gguf_models/mistral-7b-instruct-v0.2.Q4_K_M.gguf")
    if engine.load_model():
        response = engine.generate_text("What is machine learning?")
"""

import os
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

logger = logging.getLogger(__name__)

class GGUFModelInference:
    """
    GGUF Model Inference Engine for stable local AI inference.
    
    This class provides a crash-resistant alternative to transformers library
    by using pre-quantized GGUF models with llama-cpp-python.
    """
    
    def __init__(self, model_path: str, **kwargs):
        """
        Initialize GGUF model inference engine.
        
        Args:
            model_path: Path to the .gguf model file
            **kwargs: Additional configuration options
        """
        self.model_path = Path(model_path)
        self.model = None
        self.is_loaded = False
        self.config = {
            'n_ctx': kwargs.get('n_ctx', 4096),           # Context window size
            'n_gpu_layers': kwargs.get('n_gpu_layers', -1), # -1 = offload all layers to GPU
            'n_threads': kwargs.get('n_threads', None),    # CPU threads (auto-detect if None)
            'verbose': kwargs.get('verbose', False),       # Verbose output
            'use_mmap': kwargs.get('use_mmap', True),      # Memory mapping for efficiency
            'use_mlock': kwargs.get('use_mlock', False),   # Lock memory pages
        }
        
        # Check if llama-cpp-python is available
        self._llama_cpp_available = self._check_llama_cpp_availability()
        
    def _check_llama_cpp_availability(self) -> bool:
        """Check if llama-cpp-python is installed and available."""
        try:
            import llama_cpp
            logger.info("✅ llama-cpp-python is available")
            return True
        except ImportError:
            logger.warning("⚠️ llama-cpp-python not available. Install with: pip install llama-cpp-python")
            return False
    
    def is_available(self) -> bool:
        """Check if GGUF inference is available on this system."""
        return self._llama_cpp_available and self.model_path.exists()
    
    def load_model(self) -> bool:
        """
        Load the GGUF model for inference.
        
        Returns:
            bool: True if model loaded successfully, False otherwise
        """
        if not self._llama_cpp_available:
            logger.error("❌ Cannot load GGUF model: llama-cpp-python not installed")
            return False
            
        if not self.model_path.exists():
            logger.error(f"❌ GGUF model file not found: {self.model_path}")
            return False
            
        try:
            # Import llama_cpp here to avoid import errors if not installed
            from llama_cpp import Llama
            
            logger.info(f"🧠 Loading GGUF model from: {self.model_path}")
            logger.info(f"📊 Configuration: {self.config}")
            
            # Initialize the model with optimized settings for RTX 3060 12GB
            self.model = Llama(
                model_path=str(self.model_path),
                n_ctx=self.config['n_ctx'],
                n_gpu_layers=self.config['n_gpu_layers'],
                n_threads=self.config['n_threads'],
                verbose=self.config['verbose'],
                use_mmap=self.config['use_mmap'],
                use_mlock=self.config['use_mlock'],
            )
            
            self.is_loaded = True
            logger.info("✅ GGUF model loaded successfully into VRAM")
            
            # Test the model with a simple prompt
            test_response = self.model("Test", max_tokens=1, echo=False)
            logger.info("✅ Model test successful")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load GGUF model: {e}")
            self.is_loaded = False
            return False
    
    def generate_text(self, prompt: str, max_tokens: int = 512, **kwargs) -> str:
        """
        Generate text using the loaded GGUF model.
        
        Args:
            prompt: Input text prompt
            max_tokens: Maximum number of tokens to generate
            **kwargs: Additional generation parameters
            
        Returns:
            str: Generated text
        """
        if not self.is_loaded:
            raise RuntimeError("GGUF model is not loaded. Call load_model() first.")
            
        try:
            # Extract generation parameters
            temperature = kwargs.get('temperature', 0.7)
            top_p = kwargs.get('top_p', 0.9)
            top_k = kwargs.get('top_k', 40)
            repeat_penalty = kwargs.get('repeat_penalty', 1.1)
            stop_sequences = kwargs.get('stop', ["\n", "Question:", "Human:", "Assistant:"])
            
            logger.debug(f"🔄 Generating text for prompt: {prompt[:100]}...")
            
            # Generate response using llama-cpp-python
            output = self.model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                repeat_penalty=repeat_penalty,
                stop=stop_sequences,
                echo=False  # Don't include the prompt in output
            )
            
            # Extract the generated text
            generated_text = output['choices'][0]['text'].strip()
            
            logger.debug(f"✅ Generated {len(generated_text)} characters")
            return generated_text
            
        except Exception as e:
            logger.error(f"❌ Text generation failed: {e}")
            return f"Error generating text: {e}"
    
    def generate_mcq(self, context: str, topic: str = "", difficulty: str = "medium") -> Dict[str, Any]:
        """
        Generate a multiple-choice question using the GGUF model.
        
        Args:
            context: Content to generate questions about
            topic: Optional topic specification
            difficulty: Question difficulty level
            
        Returns:
            Dict containing the generated MCQ
        """
        if not self.is_loaded:
            raise RuntimeError("GGUF model is not loaded. Call load_model() first.")
            
        # Create MCQ generation prompt
        prompt = self._create_mcq_prompt(context, topic, difficulty)
        
        # Generate response
        response = self.generate_text(
            prompt,
            max_tokens=600,
            temperature=0.7,
            top_p=0.9,
            stop=["---", "\n\n\n"]
        )
        
        # Parse response into structured MCQ
        mcq = self._parse_mcq_response(response)
        return mcq
    
    def _create_mcq_prompt(self, context: str, topic: str, difficulty: str) -> str:
        """Create an optimized prompt for MCQ generation."""
        # Truncate context if too long
        max_context_length = 1000
        if len(context) > max_context_length:
            context = context[:max_context_length] + "..."
            
        topic_text = f" about {topic}" if topic else ""
        
        prompt = f"""Create a multiple-choice question{topic_text} based on this content:

{context}

Generate a {difficulty} difficulty question with 4 options (A, B, C, D) and provide the correct answer with explanation.

Format:
Question: [Your question here]
A) [Option A]
B) [Option B] 
C) [Option C]
D) [Option D]
Correct Answer: [A, B, C, or D]
Explanation: [Brief explanation]

Question:"""
        
        return prompt
    
    def _parse_mcq_response(self, response: str) -> Dict[str, Any]:
        """Parse the model response into a structured MCQ."""
        try:
            lines = response.strip().split('\n')
            
            question = ""
            options = {}
            correct_answer = ""
            explanation = ""
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                if line.startswith("Question:"):
                    question = line.replace("Question:", "").strip()
                elif line.startswith("A)"):
                    options["A"] = line[2:].strip()
                elif line.startswith("B)"):
                    options["B"] = line[2:].strip()
                elif line.startswith("C)"):
                    options["C"] = line[2:].strip()
                elif line.startswith("D)"):
                    options["D"] = line[2:].strip()
                elif line.startswith("Correct Answer:"):
                    correct_answer = line.replace("Correct Answer:", "").strip().upper()
                elif line.startswith("Explanation:"):
                    explanation = line.replace("Explanation:", "").strip()
            
            # Validate and create fallback if needed
            if not question or len(options) < 4 or not correct_answer:
                return self._create_fallback_mcq()
                
            return {
                "id": "gguf_generated",
                "question": question,
                "options": options,
                "correct": correct_answer,
                "explanation": explanation or "No explanation provided.",
                "category": "general",
                "difficulty": "medium",
                "source": "gguf_model"
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to parse MCQ response: {e}")
            return self._create_fallback_mcq()
    
    def _create_fallback_mcq(self) -> Dict[str, Any]:
        """Create a fallback MCQ when parsing fails."""
        return {
            "id": "gguf_fallback",
            "question": "Based on the provided content, what is the main topic discussed?",
            "options": {
                "A": "Technical specifications and details",
                "B": "General information and overview", 
                "C": "Historical background and context",
                "D": "Advanced theoretical concepts"
            },
            "correct": "B",
            "explanation": "This is a fallback question generated when the GGUF model response could not be parsed.",
            "category": "general",
            "difficulty": "medium",
            "source": "gguf_fallback"
        }
    
    def unload_model(self):
        """Unload the model to free memory."""
        if self.model:
            logger.info("🧹 Unloading GGUF model...")
            del self.model
            self.model = None
            self.is_loaded = False
            logger.info("✅ GGUF model unloaded successfully")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return {
            "model_path": str(self.model_path),
            "is_loaded": self.is_loaded,
            "is_available": self.is_available(),
            "config": self.config,
            "llama_cpp_available": self._llama_cpp_available
        }


# Utility functions for GGUF model management

def download_recommended_gguf_model(model_name: str = "mistral-7b-instruct") -> Optional[str]:
    """
    Download a recommended GGUF model for stable inference.
    
    This function provides guidance on downloading GGUF models from Hugging Face.
    
    Args:
        model_name: Name of the model to download
        
    Returns:
        str: Instructions for manual download (automatic download not implemented)
    """
    models = {
        "mistral-7b-instruct": {
            "repo": "TheBloke/Mistral-7B-Instruct-v0.2-GGUF",
            "file": "mistral-7b-instruct-v0.2.Q4_K_M.gguf",
            "size": "4.37 GB"
        },
        "llama-3-8b-instruct": {
            "repo": "QuantFactory/Meta-Llama-3-8B-Instruct-GGUF",
            "file": "Meta-Llama-3-8B-Instruct.Q4_K_M.gguf", 
            "size": "4.92 GB"
        }
    }
    
    if model_name not in models:
        logger.error(f"❌ Unknown model: {model_name}")
        return None
        
    model_info = models[model_name]
    
    instructions = f"""
📥 To download {model_name} GGUF model:

1. Go to: https://huggingface.co/{model_info['repo']}
2. Download: {model_info['file']} ({model_info['size']})
3. Place in: models/gguf_models/
4. Use with: GGUFModelInference("models/gguf_models/{model_info['file']}")

This model is optimized for RTX 3060 12GB and will provide stable, crash-free inference.
"""
    
    logger.info(instructions)
    return instructions
