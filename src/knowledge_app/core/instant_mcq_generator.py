"""
Instant MCQ Generator - No model loading required!

This generator creates MCQs immediately by parsing content using rule-based approaches
and simple NLP techniques. No AI model training or loading needed.
"""

import logging
import re
import random
from typing import Dict, Any, List, Optional
import asyncio

logger = logging.getLogger(__name__)

class InstantMCQGenerator:
    """Generate MCQs instantly without any model loading"""
    
    def __init__(self):
        self.is_initialized = True  # Always ready!
        
        # Common question templates
        self.question_templates = [
            "What is {concept}?",
            "Which of the following best describes {concept}?",
            "What is the main purpose of {concept}?",
            "According to the content, {concept} is primarily used for:",
            "What is a key characteristic of {concept}?",
            "Which statement about {concept} is correct?",
            "What does {concept} refer to?",
            "The term {concept} is best defined as:",
        ]
        
        # Generic distractor templates
        self.distractor_templates = [
            "A different approach to the problem",
            "An alternative method or technique", 
            "A related but distinct concept",
            "An unrelated technical term",
            "A common misconception",
            "A similar-sounding concept",
        ]
    
    async def generate_quiz_async(self, context: str, difficulty: str = "medium") -> Dict[str, Any]:
        """Generate MCQ instantly from content analysis"""
        try:
            logger.info("⚡ Generating instant MCQ from content analysis...")

            # Validate input context
            if not context or len(context.strip()) < 20:
                logger.warning("Context too short or empty, using emergency fallback")
                return self._generate_emergency_fallback(context)

            # Extract key information from content
            key_concepts = self._extract_key_concepts(context)
            definitions = self._extract_definitions(context)
            facts = self._extract_facts(context)

            logger.debug(f"Extracted concepts: {key_concepts}")
            logger.debug(f"Extracted definitions: {list(definitions.keys())}")
            logger.debug(f"Extracted facts: {len(facts)}")

            # Generate question based on available information with validation
            result = None

            if definitions and key_concepts:
                logger.info("Using definition-based generation")
                result = self._generate_definition_question(key_concepts[0], definitions, context)
            elif facts and key_concepts:
                logger.info("Using fact-based generation")
                result = self._generate_fact_question(key_concepts[0], facts, context)
            elif key_concepts:
                logger.info("Using concept-based generation")
                result = self._generate_concept_question(key_concepts[0], key_concepts, context)
            else:
                logger.info("No concepts found, using basic generation")
                result = self._generate_basic_question(context)

            # Final validation of the result
            if result and self._validate_final_result(result):
                logger.info("✅ MCQ generation successful")
                return result
            else:
                logger.warning("Generated MCQ failed final validation, using emergency fallback")
                return self._generate_emergency_fallback(context)

        except Exception as e:
            logger.error(f"❌ Instant MCQ generation failed: {e}")
            return self._generate_emergency_fallback(context)

    def _validate_final_result(self, result: Dict[str, Any]) -> bool:
        """Final validation of the generated MCQ"""
        try:
            # Check required fields
            required_fields = ['question', 'options', 'correct', 'explanation']
            for field in required_fields:
                if field not in result:
                    logger.warning(f"Missing required field: {field}")
                    return False

            # Validate question
            question = result['question']
            if not question or len(question) < 10:
                logger.warning("Question too short or empty")
                return False

            # Validate options
            options = result['options']
            if not isinstance(options, dict) or len(options) != 4:
                logger.warning("Invalid options structure")
                return False

            for key in ['A', 'B', 'C', 'D']:
                if key not in options:
                    logger.warning(f"Missing option: {key}")
                    return False
                option_text = options[key]
                if not option_text or len(option_text) < 3:
                    logger.warning(f"Option {key} too short or empty")
                    return False
                # Check for problematic content
                if any(char in option_text for char in ['[', ']', '{', '}']):
                    logger.warning(f"Option {key} contains invalid characters")
                    return False

            # Validate correct answer
            correct = result['correct']
            if correct not in ['A', 'B', 'C', 'D']:
                logger.warning("Invalid correct answer")
                return False

            return True

        except Exception as e:
            logger.error(f"Error in final validation: {e}")
            return False
    
    def _extract_key_concepts(self, text: str) -> List[str]:
        """Extract key concepts from text using improved NLP"""
        if not text or len(text.strip()) < 10:
            return []

        # Clean the text first - remove any array-like structures or special characters
        clean_text = re.sub(r'\[.*?\]', '', text)  # Remove array-like structures
        clean_text = re.sub(r'\{.*?\}', '', clean_text)  # Remove object-like structures
        clean_text = re.sub(r'[^\w\s\-\.]', ' ', clean_text)  # Keep only words, spaces, hyphens, dots

        # Extract meaningful terms using multiple patterns
        concepts = set()

        # Pattern 1: Capitalized terms (proper nouns, technical terms) - more restrictive
        capitalized = re.findall(r'\b[A-Z][a-z]{2,}(?:\s+[A-Z][a-z]{2,})*\b', clean_text)
        concepts.update(capitalized)

        # Pattern 2: Important longer words (6+ characters) - increased minimum length
        longer_words = re.findall(r'\b[a-z]{6,}\b', clean_text.lower())
        concepts.update(longer_words)

        # Pattern 3: Words that appear multiple times (likely important)
        words = re.findall(r'\b[a-zA-Z]{4,}\b', clean_text)
        word_freq = {}
        for word in words:
            word_lower = word.lower()
            word_freq[word_lower] = word_freq.get(word_lower, 0) + 1

        # Add frequently mentioned words (increased threshold)
        frequent_words = [word for word, freq in word_freq.items() if freq >= 3]
        concepts.update(frequent_words)

        # Enhanced stop words list - added more problematic words
        stop_words = {
            'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were',
            'will', 'would', 'could', 'should', 'about', 'after', 'again',
            'before', 'being', 'between', 'both', 'each', 'into', 'more',
            'most', 'other', 'some', 'such', 'than', 'their', 'them',
            'these', 'through', 'time', 'very', 'when', 'where', 'which',
            'while', 'your', 'also', 'many', 'much', 'only', 'first',
            'last', 'next', 'then', 'here', 'there', 'what', 'how',
            'content', 'question', 'following', 'mentioned', 'used',
            'example', 'include', 'provide', 'information', 'system',
            'multiple', 'generate', 'suitable', 'correct', 'statement',
            'according', 'primarily', 'functions', 'similarly', 'type'
        }

        # Filter and clean concepts with stricter validation
        filtered_concepts = []
        for concept in concepts:
            clean_concept = concept.lower().strip()
            if (len(clean_concept) > 4 and  # Increased minimum length
                clean_concept not in stop_words and
                clean_concept.isalpha() and  # Only alphabetic characters
                not clean_concept.startswith(('the', 'and', 'for')) and  # Avoid common prefixes
                clean_concept not in [c.lower() for c in filtered_concepts] and
                self._is_meaningful_concept(clean_concept)):  # Additional validation
                filtered_concepts.append(concept)

        # Sort by frequency and length, prioritizing meaningful terms
        concept_scores = {}
        for concept in filtered_concepts:
            freq_score = word_freq.get(concept.lower(), 1)
            length_score = len(concept) / 10  # Normalize length
            is_capitalized = concept[0].isupper()
            cap_score = 2 if is_capitalized else 1

            concept_scores[concept] = freq_score * length_score * cap_score

        # Return top concepts sorted by score
        sorted_concepts = sorted(filtered_concepts,
                               key=lambda x: concept_scores.get(x, 0),
                               reverse=True)

        return sorted_concepts[:5]  # Return top 5 concepts (reduced from 8)

    def _is_meaningful_concept(self, concept: str) -> bool:
        """Check if a concept is meaningful and not a fragment"""
        # Reject concepts that are too generic or problematic
        generic_words = {
            'something', 'anything', 'everything', 'nothing',
            'someone', 'anyone', 'everyone', 'nobody',
            'somewhere', 'anywhere', 'everywhere', 'nowhere',
            'sometimes', 'always', 'never', 'often',
            'important', 'different', 'similar', 'various',
            'general', 'specific', 'particular', 'certain'
        }

        if concept in generic_words:
            return False

        # Reject concepts that look like fragments or incomplete words
        if concept.endswith(('ing', 'ed', 'er', 'est', 'ly')):
            return len(concept) > 8  # Only accept longer words with these endings

        return True
    
    def _extract_definitions(self, text: str) -> Dict[str, str]:
        """Extract definitions from text using pattern matching"""
        definitions = {}
        
        # Pattern 1: "X is Y" or "X are Y"
        patterns = [
            r'([A-Z][a-zA-Z\s]+)\s+(?:is|are)\s+([^.!?]+)',
            r'([A-Z][a-zA-Z\s]+)\s*:\s*([^.!?]+)',
            r'([A-Z][a-zA-Z\s]+)\s+refers to\s+([^.!?]+)',
            r'([A-Z][a-zA-Z\s]+)\s+means\s+([^.!?]+)',
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                term = match.group(1).strip()
                definition = match.group(2).strip()
                if len(term) > 2 and len(definition) > 10:
                    definitions[term] = definition
        
        return definitions
    
    def _extract_facts(self, text: str) -> List[str]:
        """Extract factual statements from text"""
        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        
        facts = []
        for sentence in sentences:
            sentence = sentence.strip()
            # Look for factual patterns
            if (len(sentence) > 20 and 
                any(word in sentence.lower() for word in ['include', 'such as', 'example', 'used for', 'enables', 'allows', 'provides'])):
                facts.append(sentence)
        
        return facts[:5]  # Return top 5 facts
    
    def _generate_definition_question(self, concept: str, definitions: Dict[str, str], context: str) -> Dict[str, Any]:
        """Generate a definition-based question"""
        
        # Find definition for the concept or use the first available
        definition = definitions.get(concept)
        if not definition:
            concept, definition = list(definitions.items())[0]
        
        question = f"What is {concept}?"
        
        # Create options
        correct_answer = definition[:80] + "..." if len(definition) > 80 else definition
        
        # Generate distractors
        other_definitions = [d for k, d in definitions.items() if k != concept]
        distractors = []
        
        if len(other_definitions) >= 3:
            distractors = random.sample(other_definitions, 3)
        else:
            # Generate generic distractors
            distractors = [
                f"A type of software application used in computing",
                f"A mathematical formula for calculating values", 
                f"A hardware component in electronic devices"
            ]
        
        # Truncate distractors
        distractors = [d[:80] + "..." if len(d) > 80 else d for d in distractors]
        
        options = {
            "A": correct_answer,
            "B": distractors[0],
            "C": distractors[1], 
            "D": distractors[2]
        }
        
        return {
            "question": question,
            "options": options,
            "correct": "A",
            "explanation": f"According to the provided content, {concept} is defined as: {definition}",
            "instant_generated": True,
            "generation_method": "definition_analysis"
        }
    
    def _generate_fact_question(self, concept: str, facts: List[str], context: str) -> Dict[str, Any]:
        """Generate a fact-based question"""
        
        fact = facts[0]
        
        # Extract key information from the fact
        key_info = self._extract_key_info_from_fact(fact)
        
        question = f"According to the content, what is true about {concept}?"
        
        # Create correct answer from the fact
        correct_answer = key_info if key_info else fact[:60] + "..."
        
        # Generate distractors
        distractors = [
            f"It is primarily used for data storage",
            f"It requires specialized hardware to function",
            f"It was developed in the early 2000s"
        ]
        
        options = {
            "A": correct_answer,
            "B": distractors[0],
            "C": distractors[1],
            "D": distractors[2]
        }
        
        return {
            "question": question,
            "options": options,
            "correct": "A", 
            "explanation": f"Based on the provided information: {fact}",
            "instant_generated": True,
            "generation_method": "fact_analysis"
        }
    
    def _extract_key_info_from_fact(self, fact: str) -> str:
        """Extract key information from a factual statement"""
        # Look for specific patterns
        patterns = [
            r'(includes?\s+[^.]+)',
            r'(such as\s+[^.]+)',
            r'(used for\s+[^.]+)',
            r'(enables?\s+[^.]+)',
            r'(provides?\s+[^.]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, fact, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # Return first part of sentence if no pattern found
        return fact.split(',')[0] if ',' in fact else fact[:50] + "..."
    
    def _generate_concept_question(self, concept: str, concepts: List[str], context: str) -> Dict[str, Any]:
        """Generate a concept-based question"""

        # Validate the concept first
        if not concept or len(concept) < 3 or not concept.isalpha():
            logger.warning(f"Invalid concept detected: {concept}, falling back to basic question")
            return self._generate_basic_question(context)

        # Create better question templates based on the concept
        question_templates = [
            f"What is {concept} primarily used for?",
            f"According to the content, what best describes {concept}?",
            f"Which statement about {concept} is correct?",
            f"What is a key characteristic of {concept}?",
            f"How is {concept} described in the content?"
        ]

        question = random.choice(question_templates)

        # Extract context around the concept for better answers
        sentences = re.split(r'[.!?]+', context)
        relevant_sentences = []

        for sentence in sentences:
            if concept.lower() in sentence.lower():
                relevant_sentences.append(sentence.strip())

        # Create correct answer from relevant context
        correct_answer = self._extract_meaningful_answer(concept, relevant_sentences, context)

        # Generate more realistic distractors using validated concepts
        distractors = self._generate_validated_distractors(concept, concepts)

        # Validate all options before returning
        if not self._validate_question_options(question, correct_answer, distractors):
            logger.warning("Generated question failed validation, using fallback")
            return self._generate_basic_question(context)

        options = {
            "A": correct_answer,
            "B": distractors[0],
            "C": distractors[1],
            "D": distractors[2]
        }

        return {
            "question": question,
            "options": options,
            "correct": "A",
            "explanation": f"Based on the content provided, {concept} is described in the context of the main topic.",
            "instant_generated": True,
            "generation_method": "improved_concept_analysis"
        }

    def _extract_meaningful_answer(self, concept: str, relevant_sentences: List[str], context: str) -> str:
        """Extract a meaningful answer from relevant sentences"""
        if relevant_sentences:
            # Use the most informative sentence
            best_sentence = max(relevant_sentences, key=len)

            # Extract key information from the sentence
            words = best_sentence.split()
            concept_index = -1
            for i, word in enumerate(words):
                if concept.lower() in word.lower():
                    concept_index = i
                    break

            if concept_index >= 0:
                # Get context after the concept
                start_idx = max(0, concept_index - 2)
                end_idx = min(len(words), concept_index + 8)
                context_phrase = ' '.join(words[start_idx:end_idx])

                # Clean up the phrase for the answer
                if 'is' in context_phrase or 'are' in context_phrase:
                    answer_part = context_phrase.split('is')[-1].split('are')[-1].strip()
                    if len(answer_part) > 10 and not answer_part.startswith(('a', 'an', 'the')):
                        return f"It {answer_part}"
                    else:
                        return f"It is an important concept in the subject matter"
                else:
                    # Extract meaningful context
                    context_words = context_phrase.split()[-4:]
                    if len(context_words) >= 2:
                        return f"It is related to {' '.join(context_words)}"
                    else:
                        return f"It is mentioned as a key concept"
            else:
                return f"It is mentioned as an important concept"
        else:
            return f"It is a key topic discussed in the content"

    def _generate_validated_distractors(self, concept: str, concepts: List[str]) -> List[str]:
        """Generate validated distractors that make sense"""
        distractors = []

        # Use other valid concepts for distractors
        valid_other_concepts = [c for c in concepts[1:4]
                               if c.lower() != concept.lower() and
                               len(c) > 3 and c.isalpha()]

        for other_concept in valid_other_concepts:
            distractor_templates = [
                f"It is primarily related to {other_concept.lower()}",
                f"It functions similarly to {other_concept.lower()}",
                f"It is a type of {other_concept.lower()}"
            ]
            distractors.append(random.choice(distractor_templates))

        # Add generic distractors if needed
        generic_distractors = [
            "It is mainly used for data storage purposes",
            "It requires specialized hardware to operate",
            "It is primarily a theoretical concept",
            "It is used exclusively in academic research",
            "It is a legacy technology no longer in use",
            "It is a mathematical formula or equation",
            "It is a type of software application",
            "It is a hardware component or device"
        ]

        while len(distractors) < 3:
            distractors.append(random.choice(generic_distractors))

        # Ensure we have exactly 3 distractors
        return distractors[:3]

    def _validate_question_options(self, question: str, correct_answer: str, distractors: List[str]) -> bool:
        """Validate that question and options make sense"""
        # Check question
        if not question or len(question) < 10:
            return False

        # Check correct answer
        if not correct_answer or len(correct_answer) < 5:
            return False

        # Check distractors
        if len(distractors) != 3:
            return False

        for distractor in distractors:
            if not distractor or len(distractor) < 5:
                return False

        # Check for obvious duplicates or nonsensical content
        all_options = [correct_answer] + distractors
        for option in all_options:
            # Reject options with array-like content or fragments
            if '[' in option or ']' in option or '{' in option or '}' in option:
                return False
            # Reject very short or incomplete options
            if len(option.split()) < 3:
                return False

        return True
    
    def _generate_basic_question(self, context: str) -> Dict[str, Any]:
        """Generate a basic question when no specific concepts found"""

        # Get meaningful sentences
        sentences = [s.strip() for s in re.split(r'[.!?]+', context) if len(s.strip()) > 15]

        if sentences:
            # Use the longest sentence as it's likely most informative
            main_sentence = max(sentences, key=len)

            # Extract key information from the main sentence
            words = re.findall(r'\b[a-zA-Z]{3,}\b', main_sentence)

            # Filter out common words
            stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'way', 'she', 'use', 'her', 'now', 'oil', 'sit', 'set'}

            meaningful_words = [w for w in words if w.lower() not in stop_words]

            if meaningful_words:
                key_topic = meaningful_words[0]
                question = f"According to the content, what is discussed about {key_topic}?"

                # Create answer based on the sentence structure
                if 'is' in main_sentence or 'are' in main_sentence:
                    # Extract definition-like information
                    parts = re.split(r'\s+(?:is|are)\s+', main_sentence, 1)
                    if len(parts) > 1:
                        correct_answer = f"It {parts[1][:60]}..." if len(parts[1]) > 60 else f"It {parts[1]}"
                    else:
                        correct_answer = f"Information about {key_topic} and its characteristics"
                else:
                    correct_answer = f"Details about {key_topic} and related concepts"
            else:
                question = "What is the main focus of the provided content?"
                correct_answer = "The primary topic and its key aspects"
        else:
            question = "What does the content primarily discuss?"
            correct_answer = "The main subject matter presented"

        # Generate more realistic distractors
        distractors = [
            "Historical development and timeline information",
            "Technical specifications and implementation details",
            "Comparative analysis with alternative approaches"
        ]

        options = {
            "A": correct_answer,
            "B": distractors[0],
            "C": distractors[1],
            "D": distractors[2]
        }

        return {
            "question": question,
            "options": options,
            "correct": "A",
            "explanation": "This question is based on the main content and key information provided.",
            "instant_generated": True,
            "generation_method": "improved_basic_analysis"
        }
    
    def _generate_emergency_fallback(self, context: str) -> Dict[str, Any]:
        """
        Emergency fallback when everything else fails.
        Applies Three-Layered Defense - NO MORE META-LEVEL QUESTIONS!
        """
        logger.warning("🛡️ Instant MCQ Emergency Fallback - Applying Three-Layered Defense")

        # Try to extract at least one meaningful word from context
        topic = "the subject"
        if context and len(context.strip()) > 10:
            # Extract the first meaningful word for a more specific question
            words = re.findall(r'\b[a-zA-Z]{4,}\b', context)
            meaningful_words = [w for w in words if w.lower() not in {
                'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were',
                'will', 'would', 'could', 'should', 'about', 'content', 'question'
            }]

            if meaningful_words:
                topic = meaningful_words[0].lower()

        # Apply Layer 2 approach: Create content-based questions instead of meta-level
        if any(keyword in topic.lower() for keyword in ['physics', 'chemistry', 'biology', 'math', 'science']):
            return {
                "question": f"In the study of {topic}, which approach leads to the most comprehensive understanding?",
                "options": {
                    "A": "Examining the fundamental principles and their applications",
                    "B": "Memorizing formulas without understanding their derivation",
                    "C": "Focusing only on historical developments",
                    "D": "Avoiding mathematical relationships"
                },
                "correct": "A",
                "explanation": f"Comprehensive understanding of {topic} requires examining fundamental principles and how they apply in practice.",
                "instant_generated": True,
                "generation_method": "three_layer_defense_emergency"
            }
        else:
            # General content-based question avoiding meta-level patterns
            return {
                "question": f"When learning about {topic}, which strategy is most effective for deep understanding?",
                "options": {
                    "A": "Understanding the underlying processes and their relationships",
                    "B": "Memorizing isolated facts without context",
                    "C": "Focusing only on surface-level information",
                    "D": "Avoiding detailed analysis"
                },
                "correct": "A",
                "explanation": f"Deep understanding of {topic} comes from grasping underlying processes and their relationships.",
                "instant_generated": True,
                "generation_method": "three_layer_defense_emergency_general"
            }
    
    def generate_multiple_questions(self, context: str, num_questions: int = 5, difficulty: str = "medium") -> List[Dict[str, Any]]:
        """Generate multiple instant MCQs"""
        questions = []
        
        # Split content into chunks for variety
        sentences = re.split(r'[.!?]+', context)
        chunks = []
        
        # Create chunks of 2-3 sentences each
        for i in range(0, len(sentences), 2):
            chunk = '. '.join(sentences[i:i+3]).strip()
            if len(chunk) > 50:
                chunks.append(chunk)
        
        # Generate questions from different chunks
        for i in range(min(num_questions, len(chunks) + 1)):
            try:
                if i < len(chunks):
                    # Use specific chunk
                    chunk_context = chunks[i]
                else:
                    # Use full context for remaining questions
                    chunk_context = context
                
                # Run async generation in sync context
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    question = loop.run_until_complete(
                        self.generate_quiz_async(chunk_context, difficulty)
                    )
                    if question:
                        questions.append(question)
                finally:
                    loop.close()
                    
            except Exception as e:
                logger.warning(f"Failed to generate question {i+1}: {e}")
                continue
        
        return questions
