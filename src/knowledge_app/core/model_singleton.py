#!/usr/bin/env python3
"""
Global Model Singleton Manager

This module ensures that only ONE instance of the local model is loaded at any time
across the entire application, preventing the catastrophic reloading issue.

The problem: Every MCQ generation was reloading the entire 7B model from scratch,
causing massive delays and system hangs.

The solution: A global singleton that keeps the model in memory and reuses it.
"""

import logging
import threading
from typing import Optional, Dict, Any
from .local_model_inference import LocalModelInference, LocalModelConfig

logger = logging.getLogger(__name__)

class ModelSingleton:
    """
    Global singleton for managing the local model instance.
    
    Ensures only one model is loaded at a time and prevents unnecessary reloading.
    """
    
    _instance: Optional['ModelSingleton'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized') or not self._initialized:
            self.model_inference: Optional[LocalModelInference] = None
            self.current_model_id: Optional[str] = None
            self.is_loading = False
            self._model_lock = threading.Lock()
            self._initialized = True
            logger.info("🏗️ Model Singleton initialized")
    
    def get_model(self, model_id: str = "mistralai/Mistral-7B-Instruct-v0.2", 
                  config: Optional[LocalModelConfig] = None) -> Optional[LocalModelInference]:
        """
        Get the model instance, loading it if necessary.
        
        Args:
            model_id: The model identifier to load
            config: Optional model configuration
            
        Returns:
            LocalModelInference instance or None if loading failed
        """
        with self._model_lock:
            # If we already have the same model loaded, return it immediately
            if (self.model_inference is not None and 
                self.current_model_id == model_id and 
                self.model_inference.is_loaded):
                logger.info(f"✅ Reusing already loaded model: {model_id}")
                return self.model_inference
            
            # If we have a different model loaded, unload it first
            if (self.model_inference is not None and 
                self.current_model_id != model_id and 
                self.model_inference.is_loaded):
                logger.info(f"🔄 Switching from {self.current_model_id} to {model_id}")
                self.model_inference.unload_model()
                self.model_inference = None
                self.current_model_id = None
            
            # Load the new model if needed
            if self.model_inference is None or not self.model_inference.is_loaded:
                logger.info(f"🚀 Loading model singleton: {model_id}")
                self.is_loading = True
                
                try:
                    # Create config if not provided
                    if config is None:
                        config = LocalModelConfig(
                            base_model_name=model_id,
                            load_in_4bit=True,
                            max_new_tokens=600,
                            temperature=0.7,
                            top_p=0.9
                        )
                    
                    # Create new inference instance
                    self.model_inference = LocalModelInference(config)
                    
                    # Load the model
                    if self.model_inference.load_model():
                        self.current_model_id = model_id
                        logger.info(f"✅ Model singleton loaded successfully: {model_id}")
                        return self.model_inference
                    else:
                        logger.error(f"❌ Failed to load model singleton: {model_id}")
                        self.model_inference = None
                        return None
                        
                except Exception as e:
                    logger.error(f"❌ Error loading model singleton: {e}")
                    self.model_inference = None
                    return None
                    
                finally:
                    self.is_loading = False
            
            return self.model_inference
    
    def unload_model(self, force: bool = False):
        """
        Unload the current model.
        
        Args:
            force: Force unload even if model is locked
        """
        with self._model_lock:
            if self.model_inference is not None:
                logger.info(f"🧹 Unloading model singleton: {self.current_model_id}")
                self.model_inference.unload_model(force=force)
                self.model_inference = None
                self.current_model_id = None
                logger.info("✅ Model singleton unloaded")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        if self.model_inference is None or not self.model_inference.is_loaded:
            return {
                "status": "not_loaded",
                "current_model_id": None,
                "is_loading": self.is_loading
            }
        
        info = self.model_inference.get_model_info()
        info["current_model_id"] = self.current_model_id
        info["is_loading"] = self.is_loading
        return info
    
    def is_model_available(self, model_id: str = None) -> bool:
        """
        Check if a model is available and loaded.
        
        Args:
            model_id: Optional model ID to check. If None, checks if any model is loaded.
            
        Returns:
            True if the model is available and loaded
        """
        if self.model_inference is None or not self.model_inference.is_loaded:
            return False
        
        if model_id is None:
            return True
        
        return self.current_model_id == model_id


# Global singleton instance
_model_singleton = None

def get_model_singleton() -> ModelSingleton:
    """Get the global model singleton instance."""
    global _model_singleton
    if _model_singleton is None:
        _model_singleton = ModelSingleton()
    return _model_singleton

def get_model(model_id: str = "mistralai/Mistral-7B-Instruct-v0.2", 
              config: Optional[LocalModelConfig] = None) -> Optional[LocalModelInference]:
    """
    Convenience function to get a model instance.
    
    This is the main function that should be used throughout the application
    instead of creating new LocalModelInference instances.
    """
    singleton = get_model_singleton()
    return singleton.get_model(model_id, config)

def unload_global_model(force: bool = False):
    """Convenience function to unload the global model."""
    singleton = get_model_singleton()
    singleton.unload_model(force=force)

def get_global_model_info() -> Dict[str, Any]:
    """Convenience function to get global model info."""
    singleton = get_model_singleton()
    return singleton.get_model_info()
