"""
Local Model Inference System

This module provides local inference capabilities using fine-tuned models
instead of external APIs like Groq. It can load your trained LoRA adapters
and generate MCQ questions locally.
"""

# CRITICAL MEMORY FIX: Import only lightweight modules during startup
import os
import json
import logging
import threading
import time
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import re
from dataclasses import dataclass

# CRITICAL MEMORY FIX: Heavy ML imports will be done lazily when inference is first used
# This prevents loading PyTorch, transformers, peft during application startup

logger = logging.getLogger(__name__)

# Global model loading management with improved deadlock prevention
_model_loading_lock = threading.RLock()  # Use RLock for reentrant locking
_currently_loading_model = None
_loading_start_time = None
_loading_thread_id = None  # Track which thread is loading
_model_instances = {}  # Track model instances to prevent conflicts

@dataclass
class LocalModelConfig:
    """Configuration for local model inference"""
    base_model_name: str = "microsoft/DialoGPT-small"  # Default to fast, small model for UI responsiveness
    adapter_path: Optional[str] = None
    device: str = "auto"
    load_in_4bit: bool = True
    max_new_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    repetition_penalty: float = 1.1
    do_sample: bool = True

    # Fallback models to try if primary model fails
    fallback_models: List[str] = None

    def __post_init__(self):
        """Initialize fallback models if not provided"""
        if self.fallback_models is None:
            self.fallback_models = [
                # Fast, small models first (for UI responsiveness)
                "microsoft/DialoGPT-small",           # ~117MB - Very fast
                "distilgpt2",                         # ~353MB - Fast
                "gpt2",                               # ~548MB - Fast
                "microsoft/DialoGPT-medium",          # ~345MB - Fast
                # Larger instruct models (better quality but slower) - NON-GATED ONLY
                "Qwen/Qwen2.5-7B-Instruct",          # ~13GB - High quality, non-gated
                "mistralai/Mistral-7B-Instruct-v0.2", # ~13GB - High quality, non-gated
                "google/gemma-2-9b-it",               # ~18GB - High quality, non-gated
                # NOTE: Removed gated models like meta-llama/Meta-Llama-3-8B-Instruct
                # to prevent authorization errors
            ]

class LocalModelInference:
    """Local model inference for MCQ generation"""
    
    def __init__(self, config: LocalModelConfig = None):
        self.config = config or LocalModelConfig()
        self.model = None
        self.tokenizer = None
        self.device = self._get_device()
        self.is_loaded = False
        self._locked_by_generator = False  # Prevent unloading during generation
        
        # MCQ generation templates
        self.mcq_templates = {
            "system_prompt": """You are an expert educator and question generator. Your task is to create high-quality multiple-choice questions (MCQs) based on the given content. 

Guidelines:
- Create clear, unambiguous questions
- Provide 4 options (A, B, C, D) with only one correct answer
- Make distractors plausible but clearly incorrect
- Include brief explanations for the correct answer
- Focus on understanding, not memorization""",
            
            "user_prompt": """Based on the following content, generate {num_questions} multiple-choice question(s) with difficulty level: {difficulty}

Content:
{content}

Format each question as:
Question: [Question text]
A) [Option A]
B) [Option B] 
C) [Option C]
D) [Option D]
Correct Answer: [A/B/C/D]
Explanation: [Brief explanation]

---"""
        }
        
    def _import_heavy_libraries(self):
        """Import heavy ML libraries only when inference is first used"""
        try:
            # Import heavy libraries that consume significant memory
            global torch, AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig, GenerationConfig
            global PeftModel, PeftConfig

            import torch
            from transformers import (
                AutoTokenizer, AutoModelForCausalLM,
                BitsAndBytesConfig, GenerationConfig
            )
            from peft import PeftModel, PeftConfig

            logger.info("✅ Heavy ML libraries loaded for inference")

        except Exception as e:
            logger.error(f"❌ Failed to import heavy libraries for inference: {e}")
            raise Exception(f"Failed to load required AI/ML libraries for inference: {e}")

    def _get_device(self) -> str:
        """Determine the best device for inference"""
        # Import torch lazily when device detection is needed
        if not 'torch' in globals():
            self._import_heavy_libraries()

        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"

    def _get_model_device(self) -> str:
        """Get the actual device where the model is currently located"""
        if self.model is None:
            return "unknown"

        try:
            # For models with device attribute
            if hasattr(self.model, 'device'):
                return str(self.model.device)

            # For models with parameters, check first parameter device
            if hasattr(self.model, 'parameters'):
                try:
                    first_param = next(self.model.parameters())
                    return str(first_param.device)
                except StopIteration:
                    pass

            # For distributed models, check if they have hf_device_map
            if hasattr(self.model, 'hf_device_map'):
                devices = list(self.model.hf_device_map.values())
                if devices:
                    return str(devices[0])

            # Fallback to expected device
            return self.device

        except Exception as e:
            logger.warning(f"⚠️ Could not determine model device: {e}")
            return self.device
    
    def find_available_models(self) -> List[Dict[str, str]]:
        """Find available trained models and adapters"""
        models = []
        
        # Look for LoRA adapters
        adapter_dirs = [
            "data/lora_adapters_mistral",
            "data/models",
            "models",
            "checkpoints"
        ]
        
        for base_dir in adapter_dirs:
            if os.path.exists(base_dir):
                for item in os.listdir(base_dir):
                    item_path = os.path.join(base_dir, item)
                    if os.path.isdir(item_path):
                        # Check if it's a valid adapter directory
                        adapter_config = os.path.join(item_path, "adapter_config.json")
                        if os.path.exists(adapter_config):
                            try:
                                with open(adapter_config, 'r') as f:
                                    config_data = json.load(f)
                                    base_model = config_data.get("base_model_name_or_path", "Unknown")
                                    
                                models.append({
                                    "name": item,
                                    "path": item_path,
                                    "type": "LoRA Adapter",
                                    "base_model": base_model
                                })
                            except Exception as e:
                                logger.debug(f"Could not read adapter config from {adapter_config}: {e}")
        
        # Look for full fine-tuned models
        model_dirs = ["data/fine_tuned_models", "fine_tuned_models"]
        for base_dir in model_dirs:
            if os.path.exists(base_dir):
                for item in os.listdir(base_dir):
                    item_path = os.path.join(base_dir, item)
                    if os.path.isdir(item_path):
                        # Check if it's a valid model directory
                        config_file = os.path.join(item_path, "config.json")
                        if os.path.exists(config_file):
                            models.append({
                                "name": item,
                                "path": item_path,
                                "type": "Fine-tuned Model",
                                "base_model": "Self-contained"
                            })
        
        return models
    
    def load_model(self, model_path: str = None, base_model: str = None) -> bool:
        """Load a local model for inference with enhanced deadlock prevention and state management"""
        global _currently_loading_model, _loading_start_time, _loading_thread_id

        # CRITICAL MEMORY FIX: Import heavy libraries only when model loading is requested
        if not 'torch' in globals():
            logger.info("📦 Loading AI/ML libraries for inference...")
            self._import_heavy_libraries()

        # Determine model identifier
        model_id = model_path or base_model or self.config.base_model_name
        current_thread_id = threading.get_ident()

        # Check if this exact model is already loaded by this instance
        if self.is_loaded and hasattr(self, '_loaded_model_id') and self._loaded_model_id == model_id:
            # Verify model is actually ready
            if self._is_model_ready():
                logger.info(f"✅ Model {model_id} already loaded and ready")
                return True
            else:
                logger.warning(f"⚠️ Model {model_id} marked as loaded but not ready, reloading...")
                self.is_loaded = False

        # Enhanced deadlock prevention with thread tracking
        max_wait_time = 30  # Maximum wait time in seconds
        acquired_lock = False

        try:
            # Try to acquire lock with timeout
            acquired_lock = _model_loading_lock.acquire(timeout=max_wait_time)

            if not acquired_lock:
                logger.error(f"❌ Failed to acquire model loading lock within {max_wait_time}s - possible deadlock")
                logger.error(f"   Current thread: {current_thread_id}")
                logger.error(f"   Loading model: {_currently_loading_model}")
                logger.error(f"   Loading thread: {_loading_thread_id}")
                return False

            # Enhanced stale loading detection
            current_time = time.time()
            if _currently_loading_model is not None:
                loading_duration = current_time - (_loading_start_time or current_time)

                # Check for stale loading
                if loading_duration > max_wait_time:
                    logger.warning(f"⚠️ Detected stale loading for {_currently_loading_model} (duration: {loading_duration:.1f}s)")
                    logger.warning(f"   Clearing stale state and proceeding...")
                    _currently_loading_model = None
                    _loading_start_time = None
                    _loading_thread_id = None

                # Check if same model is being loaded by same thread (reentrant call)
                elif _currently_loading_model == model_id and _loading_thread_id == current_thread_id:
                    logger.warning(f"⚠️ Reentrant call detected for {model_id} in thread {current_thread_id}")
                    return False

                # Check if same model is being loaded by different thread
                elif _currently_loading_model == model_id:
                    logger.warning(f"⚠️ Model {model_id} is already being loaded by thread {_loading_thread_id}")
                    return False

                # Check if different model is loading
                elif _currently_loading_model != model_id:
                    logger.warning(f"⚠️ Another model ({_currently_loading_model}) is currently loading by thread {_loading_thread_id}")
                    return False

            # Set loading state with thread tracking
            _currently_loading_model = model_id
            _loading_start_time = current_time
            _loading_thread_id = current_thread_id

            logger.info(f"🔄 Loading local model for inference: {model_id}")

            # CRITICAL FIX: Only unload if we're loading a DIFFERENT model
            # Don't unload the same model that's already loaded!
            if self.is_loaded:
                current_model_id = getattr(self, '_current_model_id', None)
                if current_model_id == model_id:
                    logger.info(f"✅ Model {model_id} is already loaded and ready!")
                    _currently_loading_model = None
                    _loading_start_time = None
                    _loading_thread_id = None
                    return True
                else:
                    logger.info(f"🔄 Switching from {current_model_id} to {model_id}...")
                    self.unload_model()

                    # Force garbage collection and CUDA cache clearing
                    import gc
                    gc.collect()
                    gc.collect()
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()

                    # Wait a moment for cleanup to complete
                    time.sleep(1)

            # Determine model configuration
            if model_path and os.path.exists(model_path):
                # Check if it's a LoRA adapter
                adapter_config_path = os.path.join(model_path, "adapter_config.json")
                if os.path.exists(adapter_config_path):
                    # Load LoRA adapter
                    result = self._load_lora_model(model_path, base_model)
                else:
                    # Load full fine-tuned model
                    result = self._load_full_model(model_path)
            else:
                # Load base model with fallback support
                base_model = base_model or self.config.base_model_name
                result = self._load_base_model_with_fallback(base_model)

            # Store loaded model ID and verify state consistency
            if result:
                self._loaded_model_id = model_id

                # Verify model is actually ready after loading
                if self._is_model_ready():
                    logger.info(f"✅ Successfully loaded and verified model: {model_id}")
                    # Register this instance in global tracking
                    _model_instances[id(self)] = {
                        'model_id': model_id,
                        'thread_id': current_thread_id,
                        'loaded_time': time.time()
                    }
                else:
                    logger.error(f"❌ Model {model_id} loaded but failed readiness check")
                    self.is_loaded = False
                    result = False

            return result

        except Exception as e:
            logger.error(f"❌ Failed to load model {model_id}: {e}")
            return False
        finally:
            # Always clear the loading flag and release lock
            try:
                if _loading_thread_id == current_thread_id:
                    _currently_loading_model = None
                    _loading_start_time = None
                    _loading_thread_id = None
                    logger.debug(f"🧹 Cleared loading state for thread {current_thread_id}")
            except:
                pass  # Ignore cleanup errors

            if acquired_lock:
                try:
                    _model_loading_lock.release()
                    logger.debug(f"🔓 Released model loading lock for thread {current_thread_id}")
                except:
                    pass  # Ignore release errors

    def _load_base_model_with_fallback(self, primary_model: str) -> bool:
        """Load base model with automatic fallback to other instruct models"""
        # Try primary model first
        if self._load_base_model(primary_model):
            return True

        logger.warning(f"⚠️ Primary model {primary_model} failed, trying fallback models...")

        # Try fallback models
        for fallback_model in self.config.fallback_models:
            if fallback_model == primary_model:
                continue  # Skip already tried model

            logger.info(f"🔄 Trying fallback model: {fallback_model}")
            if self._load_base_model(fallback_model):
                logger.info(f"✅ Successfully loaded fallback model: {fallback_model}")
                return True

        logger.error("❌ All fallback models failed to load")
        return False
    
    def _load_base_model(self, model_name: str) -> bool:
        """Load a base model without adapters"""
        try:
            logger.info(f"📥 Loading base model: {model_name}")
            
            # Setup quantization if requested
            quantization_config = None
            if self.config.load_in_4bit:
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_quant_type="nf4",
                    bnb_4bit_use_double_quant=False,
                    llm_int8_enable_fp32_cpu_offload=True  # Enable CPU offload for large models
                )
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True,
                padding_side="left"
            )

            # Fix pad token configuration to prevent attention mask issues
            if self.tokenizer.pad_token is None:
                # For Mistral and similar models, use a different token for padding
                # to avoid attention mask ambiguity when pad_token == eos_token
                if hasattr(self.tokenizer, 'unk_token') and self.tokenizer.unk_token is not None:
                    self.tokenizer.pad_token = self.tokenizer.unk_token
                    logger.info(f"✅ Set pad_token to unk_token: {self.tokenizer.pad_token}")
                else:
                    # Fallback: use eos_token but we'll handle attention mask manually
                    self.tokenizer.pad_token = self.tokenizer.eos_token
                    logger.warning(f"⚠️ Using eos_token as pad_token: {self.tokenizer.pad_token} (may cause attention mask issues)")

            # Ensure pad_token_id is set correctly
            if self.tokenizer.pad_token_id is None:
                self.tokenizer.pad_token_id = self.tokenizer.convert_tokens_to_ids(self.tokenizer.pad_token)
            
            # Configure device mapping for RTX 3060 12GB with aggressive memory management
            if self.device == "cuda":
                # Check available GPU memory before loading
                if torch.cuda.is_available():
                    free_memory = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)
                    free_memory_gb = free_memory / (1024**3)
                    logger.info(f"🔍 Available GPU memory: {free_memory_gb:.2f}GB")

                    if free_memory_gb < 8.0:  # Less than 8GB free for 7B model
                        logger.warning(f"⚠️ Low GPU memory ({free_memory_gb:.2f}GB), using CPU offload")
                        device_map = {"": 0}  # Force single GPU with offload
                    else:
                        device_map = "auto"

                # Use offload directory for large models
                offload_folder = os.path.join(os.getcwd(), "data", "cache", "offload")
                os.makedirs(offload_folder, exist_ok=True)
            else:
                device_map = self.config.device
                offload_folder = None

            # Load model with proper device mapping
            model_kwargs = {
                "quantization_config": quantization_config,
                "device_map": device_map,
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
                "low_cpu_mem_usage": True,
            }

            # Add offload folder for large models that need it
            if offload_folder:
                model_kwargs["offload_folder"] = offload_folder

            # THE ROYAL DECREE: Apply Swift Blade of Attention to model loading
            try:
                logger.info("👑 THE ROYAL DECREE: Applying Swift Blade of Attention to model loading")

                # Import attention optimizer
                from .attention_optimizer import attention_optimizer

                # Apply attention optimization
                attention_kwargs = attention_optimizer.get_model_kwargs(model_name)
                model_kwargs.update(attention_kwargs)

                attn_impl = model_kwargs.get("attn_implementation", "eager")
                logger.info(f"🗡️ Loading {model_name} with attention implementation: {attn_impl}")
                logger.info(f"🔄 Device map: {device_map}")

                self.model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    **model_kwargs
                )
                logger.info(f"👑 ROYAL SUCCESS: Model {model_name} loaded with {attn_impl} attention!")

            except Exception as e:
                error_msg = str(e).lower()

                # Handle meta tensor errors
                if "meta tensor" in error_msg or "cannot copy out of meta tensor" in error_msg:
                    logger.warning(f"🔧 Meta tensor error detected, using alternative loading method: {e}")
                    try:
                        # Use to_empty() method for meta tensors
                        model_kwargs_meta = model_kwargs.copy()
                        model_kwargs_meta["torch_dtype"] = torch.float32  # Use float32 for stability

                        self.model = AutoModelForCausalLM.from_pretrained(
                            model_name,
                            **model_kwargs_meta
                        )

                        # Use to_empty() instead of to() for meta tensors
                        if hasattr(self.model, 'to_empty'):
                            logger.info("🔧 Using to_empty() for meta tensor handling")
                            self.model = self.model.to_empty(device=self.device)
                        else:
                            # Fallback to regular to() if to_empty() not available
                            logger.info("🔧 Fallback to regular to() method")
                            self.model = self.model.to(self.device)

                        logger.info(f"✅ Model {model_name} loaded with meta tensor handling")

                    except Exception as meta_error:
                        logger.error(f"❌ Meta tensor handling also failed: {meta_error}")
                        raise meta_error

                # Handle CUDA out of memory errors
                elif "cuda out of memory" in error_msg or "out of memory" in error_msg:
                    logger.error(f"❌ CUDA out of memory loading {model_name}: {e}")
                    # Try aggressive cleanup and retry with smaller model
                    self._aggressive_memory_cleanup()
                    raise RuntimeError(f"GPU out of memory loading {model_name}. Try using a smaller model or enable CPU offload.")

                # Handle gated model errors
                elif "gated" in error_msg or "authorization" in error_msg or "access" in error_msg:
                    logger.error(f"❌ Model {model_name} is gated and requires authorization: {e}")
                    raise RuntimeError(f"Model {model_name} requires Hugging Face authorization. Please login with 'huggingface-cli login' or use a non-gated model.")

                # Handle other errors
                else:
                    logger.error(f"❌ Failed to load model {model_name}: {e}")
                    raise e

            self.is_loaded = True
            # Track the current model ID to prevent unnecessary reloading
            self._current_model_id = model_id
            logger.info(f"✅ Base model loaded successfully: {model_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to load base model {model_name}: {e}")
            return False

    def _aggressive_memory_cleanup(self):
        """Perform aggressive memory cleanup for RTX 3060 12GB"""
        try:
            logger.info("🧹 Performing aggressive memory cleanup...")

            # Unload current model if loaded
            if self.model is not None:
                del self.model
                self.model = None

            if self.tokenizer is not None:
                del self.tokenizer
                self.tokenizer = None

            self.is_loaded = False

            # Force garbage collection multiple times
            import gc
            for _ in range(3):
                gc.collect()

            # Clear CUDA cache aggressively
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

                # Reset peak memory stats
                torch.cuda.reset_peak_memory_stats()

                # Log memory status
                allocated = torch.cuda.memory_allocated(0) / (1024**3)
                cached = torch.cuda.memory_reserved(0) / (1024**3)
                logger.info(f"🔍 After cleanup - Allocated: {allocated:.2f}GB, Cached: {cached:.2f}GB")

            # Wait for cleanup to complete
            time.sleep(2)

            logger.info("✅ Aggressive memory cleanup completed")

        except Exception as e:
            logger.warning(f"⚠️ Memory cleanup failed: {e}")
    
    def _load_lora_model(self, adapter_path: str, base_model: str = None) -> bool:
        """Load a LoRA adapter model"""
        try:
            logger.info(f"📥 Loading LoRA adapter: {adapter_path}")
            
            # Read adapter config to get base model
            adapter_config_path = os.path.join(adapter_path, "adapter_config.json")
            with open(adapter_config_path, 'r') as f:
                adapter_config = json.load(f)
                base_model = base_model or adapter_config.get("base_model_name_or_path")
            
            if not base_model:
                raise ValueError("Base model not specified and not found in adapter config")
            
            # Load base model first
            if not self._load_base_model(base_model):
                return False
            
            # Load LoRA adapter with proper device handling
            logger.info(f"🔧 Loading LoRA adapter from: {adapter_path}")

            # Create offload directory for LoRA if needed
            offload_folder = os.path.join(os.getcwd(), "data", "cache", "lora_offload")
            os.makedirs(offload_folder, exist_ok=True)

            self.model = PeftModel.from_pretrained(
                self.model,
                adapter_path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                offload_folder=offload_folder,
                device_map="auto"
            )
            
            logger.info(f"✅ LoRA model loaded successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load LoRA model: {e}")
            return False
    
    def _load_full_model(self, model_path: str) -> bool:
        """Load a full fine-tuned model"""
        try:
            logger.info(f"📥 Loading fine-tuned model: {model_path}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)

            # Fix pad token configuration to prevent attention mask issues
            if self.tokenizer.pad_token is None:
                # For Mistral and similar models, use a different token for padding
                if hasattr(self.tokenizer, 'unk_token') and self.tokenizer.unk_token is not None:
                    self.tokenizer.pad_token = self.tokenizer.unk_token
                    logger.info(f"✅ Set pad_token to unk_token: {self.tokenizer.pad_token}")
                else:
                    # Fallback: use eos_token but we'll handle attention mask manually
                    self.tokenizer.pad_token = self.tokenizer.eos_token
                    logger.warning(f"⚠️ Using eos_token as pad_token: {self.tokenizer.pad_token}")

            # Ensure pad_token_id is set correctly
            if self.tokenizer.pad_token_id is None:
                self.tokenizer.pad_token_id = self.tokenizer.convert_tokens_to_ids(self.tokenizer.pad_token)
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                device_map=self.config.device,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                low_cpu_mem_usage=True
            )
            
            self.is_loaded = True
            logger.info(f"✅ Fine-tuned model loaded successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load fine-tuned model: {e}")
            return False
    
    def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text using the loaded model with enhanced state validation and device consistency"""
        # Enhanced state validation
        if not self.is_loaded:
            raise RuntimeError("Model not loaded. Call load_model() first.")

        if not self._is_model_ready():
            error_msg = self._get_model_status_message()
            logger.error(f"❌ Model not ready for text generation: {error_msg}")
            raise RuntimeError(f"Model not ready for inference. {error_msg}")

        try:
            # Verify device consistency before generation
            model_device = self._get_model_device()

            # Handle cuda:0 vs cuda device naming
            def normalize_device(device_str):
                if device_str == "cuda:0":
                    return "cuda"
                return device_str

            normalized_model_device = normalize_device(model_device)
            normalized_expected_device = normalize_device(self.device)

            if normalized_model_device != normalized_expected_device:
                logger.warning(f"⚠️ Device mismatch detected: model on {model_device}, expected {self.device}")
                # Try to move model to correct device
                try:
                    if hasattr(self.model, 'to'):
                        logger.info(f"🔧 Moving model from {model_device} to {self.device}")
                        self.model = self.model.to(self.device)
                        model_device = self._get_model_device()
                        logger.info(f"✅ Model moved to {model_device}")
                except Exception as move_error:
                    logger.error(f"❌ Failed to move model to {self.device}: {move_error}")
                    # For cuda:0 vs cuda, this is acceptable - continue with generation
                    if "cuda" in model_device and "cuda" in self.device:
                        logger.info("💡 Continuing with cuda:0 vs cuda device difference (acceptable)")
                    else:
                        raise RuntimeError(f"Device mismatch: model on {model_device}, cannot move to {self.device}")

            # Prepare generation config with proper token handling
            # Use the actual pad_token_id if different from eos_token_id
            pad_token_id = self.tokenizer.pad_token_id
            if pad_token_id is None or pad_token_id == self.tokenizer.eos_token_id:
                # If pad_token is same as eos_token, use eos_token_id for padding
                # but ensure attention mask is handled properly (done below)
                pad_token_id = self.tokenizer.eos_token_id

            generation_config = GenerationConfig(
                max_new_tokens=kwargs.get('max_new_tokens', self.config.max_new_tokens),
                temperature=kwargs.get('temperature', self.config.temperature),
                top_p=kwargs.get('top_p', self.config.top_p),
                top_k=kwargs.get('top_k', self.config.top_k),
                repetition_penalty=kwargs.get('repetition_penalty', self.config.repetition_penalty),
                do_sample=kwargs.get('do_sample', self.config.do_sample),
                pad_token_id=pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )

            # Tokenize input with proper attention mask handling
            # For single prompts, we don't need padding, which avoids attention mask issues
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                padding=False,  # No padding for single prompt
                truncation=True,
                max_length=2048,
                return_attention_mask=True  # Explicitly request attention mask
            )

            # Ensure all input tensors are on the same device as the model
            final_device = self._get_model_device()
            logger.debug(f"🔍 Moving input tensors to device: {final_device}")
            inputs = {k: v.to(final_device) if hasattr(v, 'to') else v for k, v in inputs.items()}

            # Handle attention mask for models where pad_token == eos_token
            if self.tokenizer.pad_token_id == self.tokenizer.eos_token_id:
                # Create explicit attention mask for the input
                input_length = inputs['input_ids'].shape[1]
                attention_mask = torch.ones((1, input_length), dtype=torch.long, device=final_device)
                inputs['attention_mask'] = attention_mask
                logger.debug("🔧 Created explicit attention mask to handle pad_token == eos_token")

            # Generate with device-consistent tensors and explicit attention mask
            with torch.no_grad():
                outputs = self.model.generate(
                    input_ids=inputs['input_ids'],
                    attention_mask=inputs.get('attention_mask'),
                    generation_config=generation_config
                )

            # Decode output
            generated_text = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )

            return generated_text.strip()

        except Exception as e:
            logger.error(f"Text generation failed: {e}")
            # Check if it's a device mismatch error
            if "expected all tensors to be on the same device" in str(e).lower():
                logger.error("❌ Device mismatch error detected - this indicates model/tensor device inconsistency")
                logger.error(f"❌ Model device: {self._get_model_device()}, Expected device: {self.device}")
            return f"Error generating text: {e}"

    def generate_mcq_questions(self, content: str, num_questions: int = 5,
                              difficulty: str = "medium") -> List[Dict[str, Any]]:
        """Generate MCQ questions from content using the local model"""
        # Enhanced model state checking
        if not self._is_model_ready():
            error_msg = self._get_model_status_message()
            logger.error(f"❌ Cannot generate MCQ: {error_msg}")
            raise RuntimeError(f"Model not ready for inference. {error_msg}")

        try:
            logger.info(f"🧠 Generating {num_questions} MCQ questions (difficulty: {difficulty})")

            # Create prompt
            prompt = self._create_mcq_prompt(content, num_questions, difficulty)

            # Generate response
            response = self.generate_text(
                prompt,
                max_new_tokens=800,
                temperature=0.7,
                top_p=0.9
            )

            # Parse response into structured questions
            questions = self._parse_mcq_response(response)

            logger.info(f"✅ Generated {len(questions)} MCQ questions")
            return questions

        except Exception as e:
            logger.error(f"❌ MCQ generation failed: {e}")
            return []

    def _is_model_ready(self) -> bool:
        """Check if model is truly ready for inference with comprehensive validation"""
        try:
            # Check basic state flags
            if not self.is_loaded:
                logger.debug("Model not ready: is_loaded=False")
                return False

            # Check if model and tokenizer objects exist
            if self.model is None:
                logger.warning("Model not ready: model object is None")
                self.is_loaded = False  # Fix inconsistent state
                return False

            if self.tokenizer is None:
                logger.warning("Model not ready: tokenizer object is None")
                self.is_loaded = False  # Fix inconsistent state
                return False

            # Check if model has required attributes
            if not hasattr(self.model, 'generate'):
                logger.warning("Model not ready: model lacks generate method")
                self.is_loaded = False
                return False

            # Check if model is on correct device
            if hasattr(self.model, 'device'):
                model_device = str(self.model.device)
                if self.device == "cuda" and "cuda" not in model_device:
                    logger.warning(f"⚠️ Model device mismatch: expected {self.device}, got {model_device}")
                    # Don't fail for device mismatch, just warn

            # Verify tokenizer has required attributes
            if not hasattr(self.tokenizer, 'encode') or not hasattr(self.tokenizer, 'decode'):
                logger.warning("Model not ready: tokenizer lacks required methods")
                self.is_loaded = False
                return False

            # Check if we have a valid model ID
            if not hasattr(self, '_loaded_model_id') or not self._loaded_model_id:
                logger.warning("Model not ready: no valid model ID tracked")
                return False

            logger.debug(f"✅ Model readiness check passed for {self._loaded_model_id}")
            return True

        except Exception as e:
            logger.error(f"❌ Model readiness check failed: {e}")
            self.is_loaded = False  # Reset state on error
            return False

    def _get_model_status_message(self) -> str:
        """Get detailed model status message for debugging"""
        status_parts = []

        if not self.is_loaded:
            status_parts.append("is_loaded=False")

        if self.model is None:
            status_parts.append("model=None")

        if self.tokenizer is None:
            status_parts.append("tokenizer=None")

        if hasattr(self, '_loaded_model_id'):
            status_parts.append(f"loaded_model_id={self._loaded_model_id}")
        else:
            status_parts.append("no_model_id_tracked")

        return "Model status: " + ", ".join(status_parts) + ". Call load_model() first."

    def _create_mcq_prompt(self, content: str, num_questions: int, difficulty: str) -> str:
        """Create a prompt for MCQ generation with model-specific formatting"""
        # Truncate content if too long
        max_content_length = 1500
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."

        # Check if this is a conversational model (like DialoGPT)
        model_name = getattr(self, '_loaded_model_id', '').lower()
        is_conversational = 'dialogpt' in model_name or 'gpt' in model_name

        if is_conversational:
            # Use simpler format for conversational models
            prompt = f"""Create a multiple choice question about: {content}

Question:
A)
B)
C)
D)
Correct Answer:
Explanation: """
        else:
            # Use structured format for instruction models
            prompt = f"""<|system|>
{self.mcq_templates['system_prompt']}
<|user|>
{self.mcq_templates['user_prompt'].format(
    num_questions=num_questions,
    difficulty=difficulty,
    content=content
)}
<|assistant|>
"""
        return prompt

    def _parse_mcq_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse the model response into structured MCQ questions with enhanced fallback"""
        questions = []

        try:
            logger.debug(f"🔍 Parsing MCQ response: {response[:200]}...")

            # Split response into individual questions
            question_blocks = re.split(r'\n---\n|\n\n---\n\n', response)

            for i, block in enumerate(question_blocks):
                if not block.strip():
                    continue

                question_data = self._parse_single_mcq(block.strip(), i + 1)
                if question_data:
                    questions.append(question_data)

            # If no questions parsed successfully, try to create a fallback question
            if not questions:
                logger.warning("⚠️ No questions parsed from model response, creating fallback")
                fallback_question = self._create_fallback_mcq_question(response)
                if fallback_question:
                    questions.append(fallback_question)

            return questions

        except Exception as e:
            logger.error(f"Failed to parse MCQ response: {e}")
            # Return a fallback question even on parsing error
            fallback_question = self._create_fallback_mcq_question("")
            return [fallback_question] if fallback_question else []

    def _parse_single_mcq(self, block: str, question_id: int) -> Optional[Dict[str, Any]]:
        """Parse a single MCQ question block"""
        try:
            lines = block.split('\n')
            question_text = ""
            options = {}
            correct_answer = ""
            explanation = ""

            current_section = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                if line.startswith("Question:"):
                    question_text = line.replace("Question:", "").strip()
                    current_section = "question"
                elif line.startswith(("A)", "A.", "A:")):
                    options["A"] = line[2:].strip()
                    current_section = "options"
                elif line.startswith(("B)", "B.", "B:")):
                    options["B"] = line[2:].strip()
                elif line.startswith(("C)", "C.", "C:")):
                    options["C"] = line[2:].strip()
                elif line.startswith(("D)", "D.", "D:")):
                    options["D"] = line[2:].strip()
                elif line.startswith("Correct Answer:"):
                    correct_answer = line.replace("Correct Answer:", "").strip().upper()
                    current_section = "answer"
                elif line.startswith("Explanation:"):
                    explanation = line.replace("Explanation:", "").strip()
                    current_section = "explanation"
                elif current_section == "explanation":
                    explanation += " " + line

            # Enhanced validation with more flexible requirements
            if not question_text:
                # Try to extract question from the block content
                for line in lines:
                    if '?' in line and len(line.strip()) > 10:
                        question_text = line.strip()
                        break

                if not question_text:
                    logger.warning(f"No question text found in block {question_id}")
                    return None

            # Accept questions with at least 2 options (can be expanded)
            if len(options) < 2:
                logger.warning(f"Insufficient options in block {question_id}: {len(options)}")
                return None

            # Fill missing options if needed
            option_letters = ["A", "B", "C", "D"]
            for letter in option_letters:
                if letter not in options:
                    options[letter] = f"Option {letter} (auto-generated)"

            # Set default correct answer if missing or invalid
            if not correct_answer or correct_answer not in ["A", "B", "C", "D"]:
                correct_answer = "A"  # Default to first option
                logger.warning(f"Using default correct answer 'A' for block {question_id}")

            return {
                "id": str(question_id),
                "question": question_text,
                "options": options,
                "correct": correct_answer,
                "explanation": explanation or "No explanation provided.",
                "category": "general",
                "difficulty": "medium",
                "source": "local_model"
            }

        except Exception as e:
            logger.error(f"Failed to parse single MCQ: {e}")
            return None

    def _create_fallback_mcq_question(self, original_response: str) -> Optional[Dict[str, Any]]:
        """Create a fallback MCQ question when parsing fails"""
        try:
            # Try to extract any meaningful content from the response
            response_words = original_response.split()[:10]  # First 10 words
            topic = " ".join(response_words) if response_words else "the given topic"

            # Create a generic but valid MCQ
            question_data = {
                "id": "fallback_1",
                "question": f"Based on the content provided, what can be concluded about {topic}?",
                "options": {
                    "A": "It provides specific technical details",
                    "B": "It offers general information and context",
                    "C": "It focuses on historical background",
                    "D": "It presents advanced theoretical concepts"
                },
                "correct": "B",
                "explanation": "This is a fallback question generated when the model response could not be parsed into the expected MCQ format.",
                "category": "general",
                "difficulty": "medium",
                "source": "fallback_generator"
            }

            logger.info("✅ Created fallback MCQ question")
            return question_data

        except Exception as e:
            logger.error(f"Failed to create fallback MCQ: {e}")
            return None

    async def generate_questions_async(self, content: str, count: int = 5) -> List[Dict[str, Any]]:
        """Async wrapper for MCQ generation (for compatibility with existing code)"""
        import asyncio

        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.generate_mcq_questions,
            content,
            count
        )

    def unload_model(self, force=False):
        """Unload the model to free memory with proper device management"""
        try:
            # Check if model is locked by an active generator
            if not force and hasattr(self, '_locked_by_generator') and self._locked_by_generator:
                logger.warning("⚠️ Model is locked by active generator, skipping unload")
                logger.warning("⚠️ Use force=True to override or wait for generation to complete")
                return

            logger.info("🧹 Starting model unload process...")

            # Set flag early to prevent inference during unloading
            self.is_loaded = False
            self._current_model_id = None  # Clear model ID tracking

            if self.model:
                try:
                    # Get current model device before any operations
                    current_device = self._get_model_device()
                    logger.info(f"🔍 Model currently on device: {current_device}")

                    # Only move to CPU if model is on GPU and we can safely move it
                    if "cuda" in current_device and hasattr(self.model, 'cpu'):
                        logger.info("📤 Moving model to CPU for safe cleanup...")
                        try:
                            self.model.cpu()
                            # Wait for the move to complete
                            if torch.cuda.is_available():
                                torch.cuda.synchronize()
                            logger.info("✅ Model moved to CPU successfully")
                        except Exception as move_error:
                            logger.warning(f"⚠️ Could not move model to CPU: {move_error}")
                            # Continue with deletion anyway

                except Exception as device_error:
                    logger.warning(f"⚠️ Error checking model device: {device_error}")

                # Delete model
                logger.info("🗑️ Deleting model...")
                try:
                    del self.model
                    self.model = None
                    logger.info("✅ Model deleted successfully")
                except Exception as del_error:
                    logger.warning(f"⚠️ Error deleting model: {del_error}")
                    self.model = None  # Set to None anyway

            if self.tokenizer:
                logger.info("🗑️ Deleting tokenizer...")
                try:
                    del self.tokenizer
                    self.tokenizer = None
                    logger.info("✅ Tokenizer deleted successfully")
                except Exception as tok_error:
                    logger.warning(f"⚠️ Error deleting tokenizer: {tok_error}")
                    self.tokenizer = None  # Set to None anyway

            # Force garbage collection multiple times
            import gc
            logger.info("🧹 Running garbage collection...")
            for i in range(3):
                collected = gc.collect()
                logger.debug(f"GC cycle {i+1}: collected {collected} objects")

            # Clear GPU cache if using GPU
            if torch.cuda.is_available():
                logger.info("🧹 Clearing CUDA cache...")
                try:
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()  # Wait for all operations to complete
                    logger.info("✅ CUDA cache cleared successfully")
                except Exception as cuda_error:
                    logger.warning(f"⚠️ Error clearing CUDA cache: {cuda_error}")

            logger.info("✅ Model unloaded and memory cleared successfully")

        except Exception as e:
            logger.warning(f"⚠️ Error during model unloading: {e}")
            # Ensure state is consistent even on error
            self.model = None
            self.tokenizer = None
            self.is_loaded = False
            self._current_model_id = None  # Clear model ID tracking

            # Still try to clear CUDA cache
            if torch.cuda.is_available():
                try:
                    torch.cuda.empty_cache()
                except:
                    pass

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the currently loaded model"""
        if not self.is_loaded:
            return {"status": "not_loaded"}

        info = {
            "status": "loaded",
            "device": self.device,
            "config": {
                "max_new_tokens": self.config.max_new_tokens,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p,
                "load_in_4bit": self.config.load_in_4bit
            }
        }

        if hasattr(self.model, 'config'):
            info["model_type"] = getattr(self.model.config, 'model_type', 'unknown')
            info["vocab_size"] = getattr(self.model.config, 'vocab_size', 'unknown')

        return info

    def _aggressive_memory_cleanup(self):
        """Perform aggressive memory cleanup for RTX 3060 12GB"""
        try:
            logger.info("🧹 Performing aggressive memory cleanup...")

            # Unload current model if loaded
            if self.is_loaded:
                self.unload_model()

            # Force multiple garbage collection cycles
            import gc
            for i in range(5):
                collected = gc.collect()
                logger.debug(f"GC cycle {i+1}: collected {collected} objects")
                time.sleep(0.1)

            # Clear CUDA cache multiple times
            if torch.cuda.is_available():
                for i in range(3):
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                    time.sleep(0.2)

                # Reset CUDA memory stats
                torch.cuda.reset_peak_memory_stats()
                torch.cuda.reset_accumulated_memory_stats()

                # Log memory status
                allocated = torch.cuda.memory_allocated(0) / (1024**3)
                total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                logger.info(f"🔍 After cleanup - GPU memory: {allocated:.2f}GB / {total:.2f}GB")

            logger.info("✅ Aggressive memory cleanup completed")

        except Exception as e:
            logger.error(f"❌ Error during aggressive memory cleanup: {e}")

    def lock_model(self):
        """Lock model to prevent unloading during generation"""
        self._locked_by_generator = True
        logger.debug("🔒 Model locked by generator")

    def unlock_model(self):
        """Unlock model to allow unloading"""
        self._locked_by_generator = False
        logger.debug("🔓 Model unlocked by generator")

    def is_model_locked(self) -> bool:
        """Check if model is locked by generator"""
        return getattr(self, '_locked_by_generator', False)
