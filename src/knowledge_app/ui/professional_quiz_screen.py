"""
Professional Quiz Screen for the Knowledge App - Commercial Grade Design
Modern, clean quiz interface with professional styling and smooth interactions
"""

from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout,
    QFrame, QRadioButton, QButtonGroup, QTextEdit, QProgressBar,
    QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QColor, QPalette

from .enterprise_design_system import EnterpriseDesignSystem
from .enterprise_style_manager import EnterpriseStyleManager

# Compatibility layer for ProfessionalStyles migration
class ProfessionalStylesCompat:
    """Compatibility layer to ease migration from ProfessionalStyles to Enterprise Design System"""

    @staticmethod
    def get_design_system():
        return EnterpriseDesignSystem()

    @staticmethod
    def get_style_manager():
        return EnterpriseStyleManager()

    # Static properties for backward compatibility
    SPACING = {
        'XS': 4, 'SM': 8, 'MD': 16, 'LG': 24, 'XL': 32
    }

    COLORS = {
        'SUCCESS_COLOR': '#4CAF50',
        'ERROR_COLOR': '#F44336',
        'SURFACE_ELEVATED': '#3b3b3b',
        'SURFACE_COLOR': '#2b2b2b',
        'PRIMARY_COLOR': '#2196F3',
        'BORDER_COLOR': '#555555'
    }

    FONT_SIZES = {
        'HEADING_MEDIUM': 18
    }

    @staticmethod
    def get_card_style():
        return "border: 1px solid #555; border-radius: 12px; padding: 16px;"

# Use compatibility layer
ProfessionalStyles = ProfessionalStylesCompat
import logging

logger = logging.getLogger(__name__)

class ProfessionalQuizScreen(QWidget):
    """Professional quiz screen with modern design and smooth interactions"""
    
    # Signals
    answer_submitted = pyqtSignal(int)
    next_question_requested = pyqtSignal()
    quiz_finished = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.current_question = None
        self.option_buttons = []
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_timer)
        self.remaining_time = 0
        self.timer_active = False
        
        self.init_ui()
        self.apply_professional_styling()
        
    def init_ui(self):
        """Initialize the professional quiz interface"""
        # Main layout with professional spacing
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(
            ProfessionalStyles.SPACING['XL'],
            ProfessionalStyles.SPACING['LG'],
            ProfessionalStyles.SPACING['XL'],
            ProfessionalStyles.SPACING['LG']
        )
        main_layout.setSpacing(ProfessionalStyles.SPACING['LG'])
        self.setLayout(main_layout)
        
        # Create header with timer and progress
        self.create_header_section(main_layout)
        
        # Create question section
        self.create_question_section(main_layout)
        
        # Create options section
        self.create_options_section(main_layout)
        
        # Create feedback section
        self.create_feedback_section(main_layout)
        
        # Create action buttons section
        self.create_actions_section(main_layout)
        
    def create_header_section(self, parent_layout):
        """Create the header with timer and progress"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Progress section
        progress_container = QFrame()
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setSpacing(ProfessionalStyles.SPACING['XS'])
        
        self.progress_label = QLabel("Question 1 of 10")
        self.progress_label.setObjectName("progressLabel")
        progress_layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(10)
        progress_layout.addWidget(self.progress_bar)
        
        header_layout.addWidget(progress_container)
        
        # Spacer
        header_layout.addStretch()
        
        # Timer section
        timer_container = QFrame()
        timer_layout = QVBoxLayout(timer_container)
        timer_layout.setSpacing(ProfessionalStyles.SPACING['XS'])
        
        timer_title = QLabel("Time Remaining")
        timer_title.setObjectName("timerTitle")
        timer_title.setAlignment(Qt.AlignCenter)
        timer_layout.addWidget(timer_title)
        
        self.timer_label = QLabel("05:00")
        self.timer_label.setObjectName("timerLabel")
        self.timer_label.setAlignment(Qt.AlignCenter)
        timer_layout.addWidget(self.timer_label)
        
        header_layout.addWidget(timer_container)
        
        parent_layout.addWidget(header_frame)
        
    def create_question_section(self, parent_layout):
        """Create the question display section"""
        question_frame = QFrame()
        question_frame.setObjectName("questionFrame")
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 20))
        shadow.setOffset(0, 2)
        question_frame.setGraphicsEffect(shadow)
        
        question_layout = QVBoxLayout(question_frame)
        question_layout.setContentsMargins(
            ProfessionalStyles.SPACING['XL'],
            ProfessionalStyles.SPACING['LG'],
            ProfessionalStyles.SPACING['XL'],
            ProfessionalStyles.SPACING['LG']
        )
        
        # Question number and category
        self.question_meta = QLabel("Question 1 • Physics")
        self.question_meta.setObjectName("questionMeta")
        question_layout.addWidget(self.question_meta)
        
        # Question text
        self.question_label = QLabel()
        self.question_label.setObjectName("questionLabel")
        self.question_label.setWordWrap(True)
        self.question_label.setMinimumHeight(80)
        question_layout.addWidget(self.question_label)
        
        parent_layout.addWidget(question_frame)
        
    def create_options_section(self, parent_layout):
        """Create the options selection section"""
        options_frame = QFrame()
        options_frame.setObjectName("optionsFrame")
        options_layout = QVBoxLayout(options_frame)
        options_layout.setSpacing(ProfessionalStyles.SPACING['MD'])
        
        # Options group
        self.options_group = QButtonGroup(self)
        self.options_layout = QVBoxLayout()
        self.options_layout.setSpacing(ProfessionalStyles.SPACING['SM'])
        
        options_layout.addLayout(self.options_layout)
        parent_layout.addWidget(options_frame)
        
    def create_feedback_section(self, parent_layout):
        """Create the feedback section"""
        self.feedback_container = QFrame()
        self.feedback_container.setObjectName("feedbackContainer")
        self.feedback_container.setVisible(False)
        
        feedback_layout = QVBoxLayout(self.feedback_container)
        feedback_layout.setContentsMargins(
            ProfessionalStyles.SPACING['LG'],
            ProfessionalStyles.SPACING['MD'],
            ProfessionalStyles.SPACING['LG'],
            ProfessionalStyles.SPACING['MD']
        )
        
        # Feedback header
        self.feedback_header = QLabel()
        self.feedback_header.setObjectName("feedbackHeader")
        self.feedback_header.setAlignment(Qt.AlignCenter)
        feedback_layout.addWidget(self.feedback_header)
        
        # Explanation
        explanation_title = QLabel("Explanation:")
        explanation_title.setObjectName("explanationTitle")
        feedback_layout.addWidget(explanation_title)
        
        self.explanation_text = QTextEdit()
        self.explanation_text.setObjectName("explanationText")
        self.explanation_text.setReadOnly(True)
        self.explanation_text.setMaximumHeight(100)
        feedback_layout.addWidget(self.explanation_text)
        
        parent_layout.addWidget(self.feedback_container)
        
    def create_actions_section(self, parent_layout):
        """Create the action buttons section"""
        actions_frame = QFrame()
        actions_frame.setObjectName("actionsFrame")
        actions_layout = QHBoxLayout(actions_frame)
        actions_layout.setSpacing(ProfessionalStyles.SPACING['MD'])
        
        # Back to menu button
        self.back_btn = QPushButton("Back to Menu")
        self.back_btn.setObjectName("backButton")
        self.back_btn.clicked.connect(self._on_back_to_menu)
        actions_layout.addWidget(self.back_btn)
        
        # Spacer
        actions_layout.addStretch()
        
        # Submit button
        self.submit_btn = QPushButton("Submit Answer")
        self.submit_btn.setObjectName("submitButton")
        self.submit_btn.clicked.connect(self.check_answer)
        self.submit_btn.setEnabled(False)
        actions_layout.addWidget(self.submit_btn)
        
        # Next question button
        self.next_btn = QPushButton("Next Question")
        self.next_btn.setObjectName("nextButton")
        self.next_btn.clicked.connect(self.next_question)
        self.next_btn.setVisible(False)
        actions_layout.addWidget(self.next_btn)
        
        # Finish quiz button
        self.finish_btn = QPushButton("Finish Quiz")
        self.finish_btn.setObjectName("finishButton")
        self.finish_btn.clicked.connect(self.finish_quiz)
        self.finish_btn.setVisible(False)
        actions_layout.addWidget(self.finish_btn)
        
        parent_layout.addWidget(actions_frame)
        
    def apply_professional_styling(self):
        """Apply comprehensive enterprise styling"""
        try:
            # Initialize enterprise design system if not already done
            if not hasattr(self, 'design_system'):
                self.design_system = EnterpriseDesignSystem()
            if not hasattr(self, 'style_manager'):
                self.style_manager = EnterpriseStyleManager()

            style = f"""
                /* Main Container */
                ProfessionalQuizScreen {{
                    background: {self.design_system.color('bg_primary')};
                    color: {self.design_system.color('text_primary')};
                }}

                /* Header Styling */
                #headerFrame {{
                    background: transparent;
                    border: none;
                    padding: 0px;
                }}

                #progressLabel {{
                    color: {self.design_system.color('text_secondary')};
                    font-size: {self.design_system.font_size('body_medium')}px;
                    font-weight: 500;
                }}

                #progressBar {{
                    {self.style_manager.get_style('progress_bar')}
                }}

                #timerTitle {{
                    color: {self.design_system.color('text_muted')};
                    font-size: {self.design_system.font_size('body_small')}px;
                }}
            
            #timerLabel {{
                color: {self.design_system.color('primary')};
                background: {self.design_system.color('surface')};
                border: 2px solid {self.design_system.color('primary')};
                border-radius: 8px;
                padding: {self.design_system.spacing('sm')}px {self.design_system.spacing('md')}px;
                min-width: 80px;
                font-size: {self.design_system.font_size('heading_large')}px;
                font-weight: 700;
            }}
            
            /* Question Section */
            #questionFrame {{
                background: {self.design_system.color('surface')};
                border: 1px solid {self.design_system.color('border')};
                border-radius: 12px;
                padding: {self.design_system.spacing('lg')}px;
                min-height: 120px;
            }}

            #questionMeta {{
                color: {self.design_system.color('primary')};
                font-size: {self.design_system.font_size('body_small')}px;
                font-weight: 500;
                margin-bottom: {self.design_system.spacing('sm')}px;
            }}

            #questionLabel {{
                color: {self.design_system.color('text_primary')};
                font-size: {self.design_system.font_size('heading_medium')}px;
                line-height: 1.5;
            }}

            /* Options Section */
            #optionsFrame {{
                background: transparent;
                border: none;
            }}

            /* Radio Buttons */
            QRadioButton {{
                background: {self.design_system.color('surface')};
                border: 2px solid {self.design_system.color('border')};
                border-radius: 8px;
                padding: {self.design_system.spacing('md')}px;
                margin: {self.design_system.spacing('xs')}px 0px;
                min-height: 40px;
                color: {self.design_system.color('text_primary')};
            }}

            QRadioButton:hover {{
                background: {self.design_system.color('surface_hover')};
                border-color: {self.design_system.color('primary')};
            }}

            QRadioButton:checked {{
                background: {self.design_system.color('primary')};
                color: {self.design_system.color('text_on_primary')};
                border-color: {self.design_system.color('primary')};
            }}
            
            /* Feedback Section */
            #feedbackContainer {{
                background: {self.design_system.color('surface_elevated')};
                border: 1px solid {self.design_system.color('border')};
                border-left: 4px solid {self.design_system.color('success')};
                border-radius: 12px;
                padding: {self.design_system.spacing('lg')}px;
            }}

            #feedbackHeader {{
                font-size: {self.design_system.font_size('heading_medium')}px;
                font-weight: 700;
                margin-bottom: {self.design_system.spacing('sm')}px;
            }}

            #explanationTitle {{
                color: {self.design_system.color('text_secondary')};
                font-size: {self.design_system.font_size('body_medium')}px;
                font-weight: 500;
                margin-bottom: {self.design_system.spacing('xs')}px;
            }}

            #explanationText {{
                background: {self.design_system.color('bg_secondary')};
                border: 1px solid {self.design_system.color('border')};
                border-radius: 8px;
                padding: {self.design_system.spacing('md')}px;
                color: {self.design_system.color('text_primary')};
            }}

            /* Action Buttons */
            #backButton {{
                {self.style_manager.get_style('button_secondary')}
            }}

            #submitButton {{
                {self.style_manager.get_style('button_primary')}
            }}

            #nextButton {{
                {self.style_manager.get_style('button_success')}
            }}

            #finishButton {{
                {self.style_manager.get_style('button_warning')}
            }}
        """

            self.setStyleSheet(style)

        except Exception as e:
            logger.error(f"Error applying enterprise styling: {e}")
            # Fallback to basic styling
            self.setStyleSheet("background-color: #2b2b2b; color: white;")

    def display_question(self, question_data):
        """Display a question with professional formatting"""
        try:
            # Store current question
            self.current_question = question_data

            # Clear previous state
            self.clear_question_display()

            # Update question metadata
            question_num = question_data.get('question_number', 1)
            category = question_data.get('category', 'General')
            self.question_meta.setText(f"Question {question_num} • {category}")

            # Display question text
            self.question_label.setText(question_data['question'])

            # Handle both dictionary and list formats for options
            options = question_data['options']
            if isinstance(options, dict):
                # Dictionary format: {"A": "content", "B": "content", ...}
                option_items = []
                for key in sorted(options.keys()):
                    option_items.append((key, options[key]))
            elif isinstance(options, list):
                # List format: ["content1", "content2", ...]
                option_items = []
                for i, content in enumerate(options):
                    letter = chr(ord('A') + i)
                    option_items.append((letter, content))
            else:
                logger.warning(f"Unexpected options format: {type(options)}")
                option_items = [('A', str(options))]

            # Create option buttons with professional styling
            for i, (letter, text) in enumerate(option_items):
                radio = QRadioButton(f"{letter}) {text}")
                radio.setObjectName(f"option_{letter}")

                # Connect selection event
                radio.toggled.connect(self.option_selected)

                self.options_layout.addWidget(radio)
                self.options_group.addButton(radio, i)
                self.option_buttons.append(radio)

            # Reset UI state
            self.submit_btn.setVisible(True)
            self.submit_btn.setEnabled(False)
            self.feedback_container.setVisible(False)
            self.next_btn.setVisible(False)
            self.finish_btn.setVisible(False)

            logger.info(f"Displayed question: {question_data['question'][:50]}...")

        except Exception as e:
            logger.error(f"Error displaying question: {e}")
            self.show_error("Failed to display question")

    def clear_question_display(self):
        """Clear the current question display"""
        # Clear option buttons
        for btn in self.option_buttons:
            self.options_layout.removeWidget(btn)
            btn.deleteLater()
        self.option_buttons.clear()

        # Reset feedback
        self.feedback_container.setVisible(False)

    def option_selected(self):
        """Handle option selection event"""
        try:
            # Enable submit button when an option is selected
            self.submit_btn.setEnabled(True)

            # Add visual feedback for selection
            sender = self.sender()
            if sender and isinstance(sender, QRadioButton):
                logger.debug(f"Option selected: {sender.text()}")

        except Exception as e:
            logger.error(f"Error handling option selection: {e}")

    def check_answer(self):
        """Check the submitted answer with professional feedback"""
        try:
            # Get selected answer
            selected_id = self.options_group.checkedId()
            if selected_id == -1:
                self.show_error("Please select an answer")
                return

            # Stop timer if active
            if self.timer_active:
                self.stop_timer()

            # Get correct answer
            selected_letter = chr(ord('A') + selected_id)

            # Handle different correct answer formats
            if 'correct_answer' in self.current_question:
                correct_letter = self.current_question['correct_answer'].upper()
            elif 'correct' in self.current_question:
                correct_letter = self.current_question['correct'].upper()
            else:
                correct_letter = 'A'  # Default fallback
                logger.warning("No correct answer field found in question data")

            is_correct = selected_letter == correct_letter

            # Show professional feedback
            self.show_feedback(is_correct, correct_letter)

            # Disable options and submit button
            for btn in self.option_buttons:
                btn.setEnabled(False)
            self.submit_btn.setEnabled(False)
            self.submit_btn.setVisible(False)

            # Show navigation buttons
            self.next_btn.setVisible(True)
            self.finish_btn.setVisible(True)

            # Emit signal
            self.answer_submitted.emit(selected_id)

            logger.info(f"Answer checked: {'Correct' if is_correct else 'Incorrect'}")

        except Exception as e:
            logger.error(f"Error checking answer: {e}")
            self.show_error("Failed to check answer")

    def show_feedback(self, is_correct, correct_letter):
        """Show professional feedback for the answer"""
        try:
            # Set feedback header with appropriate styling
            if is_correct:
                self.feedback_header.setText("🎉 Correct!")
                self.feedback_header.setStyleSheet(f"""
                    color: {ProfessionalStyles.COLORS['SUCCESS_COLOR']};
                    font-size: {ProfessionalStyles.FONT_SIZES['HEADING_MEDIUM']}px;
                    font-weight: 700;
                """)
                # Update container border color
                self.feedback_container.setStyleSheet(f"""
                    #feedbackContainer {{
                        {ProfessionalStyles.get_card_style()}
                        background: {ProfessionalStyles.COLORS['SURFACE_ELEVATED']};
                        border-left: 4px solid {ProfessionalStyles.COLORS['SUCCESS_COLOR']};
                    }}
                """)
            else:
                self.feedback_header.setText(f"❌ Incorrect. The correct answer was {correct_letter}.")
                self.feedback_header.setStyleSheet(f"""
                    color: {ProfessionalStyles.COLORS['ERROR_COLOR']};
                    font-size: {ProfessionalStyles.FONT_SIZES['HEADING_MEDIUM']}px;
                    font-weight: 700;
                """)
                # Update container border color
                self.feedback_container.setStyleSheet(f"""
                    #feedbackContainer {{
                        {ProfessionalStyles.get_card_style()}
                        background: {ProfessionalStyles.COLORS['SURFACE_ELEVATED']};
                        border-left: 4px solid {ProfessionalStyles.COLORS['ERROR_COLOR']};
                    }}
                """)

            # Set explanation
            explanation = self.current_question.get('explanation', 'No explanation provided.')
            self.explanation_text.setText(explanation)

            # Show feedback container with animation
            self.feedback_container.setVisible(True)

        except Exception as e:
            logger.error(f"Error showing feedback: {e}")

    def show_error(self, message):
        """Show error message with professional styling"""
        # This could be enhanced with a proper error dialog
        logger.error(message)

    def next_question(self):
        """Request the next question"""
        try:
            # Clear current question display
            self.clear_question_display()

            # Show loading state
            self.question_label.setText("Loading next question...")
            self.submit_btn.setEnabled(False)
            self.next_btn.setEnabled(False)

            # Try synchronous question generation first (immediate response)
            print(f"DEBUG_PROF_QUIZ: Parent type: {type(self.parent)}")
            print(f"DEBUG_PROF_QUIZ: Has get_next_question_sync: {hasattr(self.parent, 'get_next_question_sync')}")
            if hasattr(self.parent, 'get_next_question_sync'):
                print("DEBUG_PROF_QUIZ: Calling get_next_question_sync...")
                logger.info("🔄 Using synchronous question generation in ProfessionalQuizScreen")
                try:
                    question_data = self.parent.get_next_question_sync()
                    print(f"DEBUG_PROF_QUIZ: Got question data: {question_data is not None}")
                    if question_data:
                        logger.info("✅ Got question synchronously, displaying it")
                        self.display_question(question_data)
                        self.next_question_requested.emit()
                        return
                    else:
                        logger.warning("⚠️ Synchronous generation returned None")
                except Exception as e:
                    logger.warning(f"⚠️ Synchronous generation failed: {e}")

            # Fall back to asynchronous generation (triggers display later)
            elif hasattr(self.parent, 'generate_next_question'):
                logger.info("🔄 Using asynchronous question generation (fallback)")
                self.parent.generate_next_question()
            elif hasattr(self.parent, 'start_new_quiz_from_setup'):
                # Fallback to old method
                logger.info("🔄 Using start_new_quiz_from_setup (legacy fallback)")
                self.parent.start_new_quiz_from_setup(
                    getattr(self.parent, 'current_topic', 'General'),
                    getattr(self.parent, 'current_mode', 'Casual'),
                    getattr(self.parent, 'current_submode', 'Multiple Choice'),
                    getattr(self.parent, 'current_difficulty', 'medium')
                )
            else:
                logger.error("❌ No question generation method available in parent")
                self.show_error("No question generation method available")
                return

            # Emit signal
            self.next_question_requested.emit()

        except Exception as e:
            logger.error(f"Error requesting next question: {e}")
            self.show_error("Failed to load next question")

    def finish_quiz(self):
        """Finish the current quiz"""
        try:
            logger.info("Quiz finished")
            if self.parent and hasattr(self.parent, 'stack') and hasattr(self.parent, 'main_menu'):
                self.parent.stack.setCurrentWidget(self.parent.main_menu)

            # Emit signal
            self.quiz_finished.emit()

        except Exception as e:
            logger.error(f"Error finishing quiz: {e}")

    def _on_back_to_menu(self):
        """Handle back to menu action"""
        try:
            if self.parent and hasattr(self.parent, 'stack') and hasattr(self.parent, 'main_menu'):
                self.parent.stack.setCurrentWidget(self.parent.main_menu)
        except Exception as e:
            logger.error(f"Error going back to menu: {e}")

    def set_quiz_mode(self, mode):
        """Configure the quiz screen based on mode"""
        try:
            if mode == "Serious":
                # Enable timer for serious mode
                self.timer_label.setVisible(True)
                self.start_timer(300)  # 5 minutes per question
            else:
                # Disable timer for casual mode
                self.timer_label.setVisible(False)
                self.stop_timer()
        except Exception as e:
            logger.error(f"Error setting quiz mode: {e}")

    def start_timer(self, duration):
        """Start the quiz timer"""
        try:
            self.remaining_time = duration
            self.timer_active = True
            self.timer.start(1000)  # Update every second
            self.update_timer()
        except Exception as e:
            logger.error(f"Error starting timer: {e}")

    def stop_timer(self):
        """Stop the quiz timer"""
        try:
            self.timer_active = False
            self.timer.stop()
        except Exception as e:
            logger.error(f"Error stopping timer: {e}")

    def update_timer(self):
        """Update the timer display"""
        try:
            if not self.timer_active:
                return

            self.remaining_time -= 1
            minutes = self.remaining_time // 60
            seconds = self.remaining_time % 60
            self.timer_label.setText(f"{minutes:02d}:{seconds:02d}")

            # Change color when time is running low
            if self.remaining_time <= 30:
                self.timer_label.setStyleSheet(f"""
                    color: {ProfessionalStyles.COLORS['ERROR_COLOR']};
                    background: {ProfessionalStyles.COLORS['SURFACE_COLOR']};
                    border: 2px solid {ProfessionalStyles.COLORS['ERROR_COLOR']};
                    border-radius: 8px;
                    padding: {ProfessionalStyles.SPACING['SM']}px {ProfessionalStyles.SPACING['MD']}px;
                    font-weight: bold;
                """)

            if self.remaining_time <= 0:
                self.handle_timeout()

        except Exception as e:
            logger.error(f"Error updating timer: {e}")

    def handle_timeout(self):
        """Handle timer timeout"""
        try:
            self.stop_timer()
            self.show_error("Time's up! Please submit your answer.")
            self.check_answer()  # Auto-submit
        except Exception as e:
            logger.error(f"Error handling timeout: {e}")

    def update_progress(self, current, total):
        """Update the progress display"""
        try:
            self.progress_label.setText(f"Question {current} of {total}")
            progress_percent = int((current / total) * 100)
            self.progress_bar.setValue(progress_percent)
        except Exception as e:
            logger.error(f"Error updating progress: {e}")
