"""
Enhanced MCQ Widget with LaTeX support and rich formatting
"""

import logging
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QRadioButton, 
    QButtonGroup, QTextEdit, QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap

from ..latex_renderer import render_text_with_latex, detect_latex_in_text
from ..ui.enterprise_style_manager import get_style_manager, get_design_system

logger = logging.getLogger(__name__)


class EnhancedMCQWidget(QWidget):
    """Enhanced MCQ widget with LaTeX support and professional styling"""
    
    option_selected = pyqtSignal(int)  # Emits selected option index
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Initialize styling
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()
        
        # MCQ data
        self.mcq_data = None
        self.option_buttons = []
        self.button_group = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the enhanced UI with LaTeX support"""
        layout = QVBoxLayout(self)
        layout.setSpacing(self.design_system.spacing('lg'))
        layout.setContentsMargins(
            self.design_system.spacing('xl'),
            self.design_system.spacing('lg'),
            self.design_system.spacing('xl'),
            self.design_system.spacing('lg')
        )
        
        # Question container
        self.question_container = QFrame()
        self.question_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.design_system.color('surface')};
                border: 1px solid {self.design_system.color('border')};
                border-radius: {self.design_system.radius('lg')}px;
                padding: {self.design_system.spacing('lg')}px;
            }}
        """)
        
        question_layout = QVBoxLayout(self.question_container)
        
        # Question label for LaTeX content
        self.question_label = QLabel()
        self.question_label.setWordWrap(True)
        self.question_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.question_label.setStyleSheet(f"""
            QLabel {{
                color: {self.design_system.color('text_primary')};
                font-size: {self.design_system.font_size('lg')}px;
                font-weight: {self.design_system._typography['font_weight_semibold']};
                line-height: {self.design_system._typography['line_height_relaxed']};
                padding: {self.design_system.spacing('md')}px;
                background-color: transparent;
                border: none;
            }}
        """)
        question_layout.addWidget(self.question_label)
        
        layout.addWidget(self.question_container)
        
        # Options container
        self.options_container = QFrame()
        self.options_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.design_system.color('surface')};
                border: 1px solid {self.design_system.color('border')};
                border-radius: {self.design_system.radius('lg')}px;
                padding: {self.design_system.spacing('md')}px;
            }}
        """)
        
        self.options_layout = QVBoxLayout(self.options_container)
        self.options_layout.setSpacing(self.design_system.spacing('sm'))
        
        # Create button group for exclusive selection
        self.button_group = QButtonGroup(self)
        self.button_group.buttonClicked.connect(self._on_option_selected)
        
        layout.addWidget(self.options_container)
        
        # Explanation container (initially hidden)
        self.explanation_container = QFrame()
        self.explanation_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.design_system.color('surface_secondary')};
                border: 1px solid {self.design_system.color('border')};
                border-radius: {self.design_system.radius('lg')}px;
                padding: {self.design_system.spacing('lg')}px;
            }}
        """)
        
        explanation_layout = QVBoxLayout(self.explanation_container)
        
        self.explanation_header = QLabel("Explanation")
        self.explanation_header.setStyleSheet(f"""
            QLabel {{
                color: {self.design_system.color('text_primary')};
                font-size: {self.design_system.font_size('md')}px;
                font-weight: {self.design_system._typography['font_weight_bold']};
                margin-bottom: {self.design_system.spacing('sm')}px;
            }}
        """)
        explanation_layout.addWidget(self.explanation_header)
        
        self.explanation_label = QLabel()
        self.explanation_label.setWordWrap(True)
        self.explanation_label.setStyleSheet(f"""
            QLabel {{
                color: {self.design_system.color('text_secondary')};
                font-size: {self.design_system.font_size('base')}px;
                line-height: {self.design_system._typography['line_height_relaxed']};
                padding: {self.design_system.spacing('sm')}px;
            }}
        """)
        explanation_layout.addWidget(self.explanation_label)
        
        layout.addWidget(self.explanation_container)
        self.explanation_container.hide()
        
    def display_mcq(self, mcq_data: Dict[str, Any]):
        """Display an MCQ with enhanced formatting and LaTeX support"""
        try:
            self.mcq_data = mcq_data
            
            # Display question with LaTeX support
            question_text = mcq_data.get('question', '')
            self._display_question_with_latex(question_text)
            
            # Clear existing options
            self._clear_options()
            
            # Display options
            options = mcq_data.get('options', {})
            if isinstance(options, dict):
                option_items = sorted(options.items())
            else:
                # Handle list format
                option_items = [(chr(ord('A') + i), opt) for i, opt in enumerate(options)]
            
            for letter, option_text in option_items:
                self._add_option(letter, option_text)
                
            logger.info(f"Displayed MCQ with {len(option_items)} options")
            
        except Exception as e:
            logger.error(f"Error displaying MCQ: {e}")
            self._display_error_message("Error displaying question")
            
    def _display_question_with_latex(self, question_text: str):
        """Display question text with LaTeX rendering if needed"""
        try:
            if detect_latex_in_text(question_text):
                # Render LaTeX to pixmap
                pixmap = render_text_with_latex(
                    question_text,
                    font_size=self.design_system.font_size('lg'),
                    max_width=800,
                    background_color=self.design_system.color('surface')
                )
                self.question_label.setPixmap(pixmap)
                self.question_label.setText("")  # Clear text when using pixmap
            else:
                # Display as regular text
                self.question_label.setPixmap(QPixmap())  # Clear pixmap
                self.question_label.setText(question_text)
                
        except Exception as e:
            logger.error(f"Error rendering question with LaTeX: {e}")
            # Fallback to plain text
            self.question_label.setPixmap(QPixmap())
            self.question_label.setText(question_text)
            
    def _clear_options(self):
        """Clear existing option buttons"""
        for button in self.option_buttons:
            self.button_group.removeButton(button)
            button.deleteLater()
        self.option_buttons.clear()
        
    def _add_option(self, letter: str, option_text: str):
        """Add an option button with LaTeX support"""
        try:
            option_button = QRadioButton()
            option_button.setStyleSheet(f"""
                QRadioButton {{
                    color: {self.design_system.color('text_primary')};
                    font-size: {self.design_system.font_size('base')}px;
                    padding: {self.design_system.spacing('md')}px;
                    margin: {self.design_system.spacing('xs')}px 0px;
                    border-radius: {self.design_system.radius('md')}px;
                    background-color: transparent;
                }}
                QRadioButton:hover {{
                    background-color: {self.design_system.color('surface_hover')};
                }}
                QRadioButton:checked {{
                    background-color: {self.design_system.color('primary_light')};
                    color: {self.design_system.color('primary')};
                    font-weight: {self.design_system._typography['font_weight_semibold']};
                }}
                QRadioButton::indicator {{
                    width: 18px;
                    height: 18px;
                    margin-right: {self.design_system.spacing('sm')}px;
                }}
                QRadioButton::indicator:unchecked {{
                    border: 2px solid {self.design_system.color('border')};
                    border-radius: 9px;
                    background-color: transparent;
                }}
                QRadioButton::indicator:checked {{
                    border: 2px solid {self.design_system.color('primary')};
                    border-radius: 9px;
                    background-color: {self.design_system.color('primary')};
                }}
            """)
            
            # Set option text with LaTeX support
            if detect_latex_in_text(option_text):
                # For now, use plain text for options to avoid complexity
                # LaTeX in options can be added later if needed
                option_button.setText(f"{letter}) {option_text}")
            else:
                option_button.setText(f"{letter}) {option_text}")
            
            # Store the option letter for identification
            option_button.option_letter = letter
            
            self.button_group.addButton(option_button)
            self.option_buttons.append(option_button)
            self.options_layout.addWidget(option_button)
            
        except Exception as e:
            logger.error(f"Error adding option {letter}: {e}")
            
    def _on_option_selected(self, button):
        """Handle option selection"""
        try:
            option_letter = getattr(button, 'option_letter', 'A')
            option_index = ord(option_letter) - ord('A')
            self.option_selected.emit(option_index)
            logger.debug(f"Option selected: {option_letter} (index: {option_index})")
        except Exception as e:
            logger.error(f"Error handling option selection: {e}")
            
    def show_explanation(self, is_correct: bool, correct_answer: str):
        """Show explanation with styling based on correctness"""
        try:
            if not self.mcq_data:
                return
                
            explanation_text = self.mcq_data.get('explanation', 'No explanation available.')
            
            # Update explanation with LaTeX support
            if detect_latex_in_text(explanation_text):
                pixmap = render_text_with_latex(
                    explanation_text,
                    font_size=self.design_system.font_size('base'),
                    max_width=700,
                    background_color=self.design_system.color('surface_secondary')
                )
                self.explanation_label.setPixmap(pixmap)
                self.explanation_label.setText("")
            else:
                self.explanation_label.setPixmap(QPixmap())
                self.explanation_label.setText(explanation_text)
            
            # Update header based on correctness
            if is_correct:
                self.explanation_header.setText("✅ Correct!")
                self.explanation_header.setStyleSheet(f"""
                    QLabel {{
                        color: {self.design_system.color('success')};
                        font-size: {self.design_system.font_size('md')}px;
                        font-weight: {self.design_system._typography['font_weight_bold']};
                        margin-bottom: {self.design_system.spacing('sm')}px;
                    }}
                """)
            else:
                self.explanation_header.setText(f"❌ Incorrect. Correct answer: {correct_answer}")
                self.explanation_header.setStyleSheet(f"""
                    QLabel {{
                        color: {self.design_system.color('error')};
                        font-size: {self.design_system.font_size('md')}px;
                        font-weight: {self.design_system._typography['font_weight_bold']};
                        margin-bottom: {self.design_system.spacing('sm')}px;
                    }}
                """)
            
            self.explanation_container.show()
            
        except Exception as e:
            logger.error(f"Error showing explanation: {e}")
            
    def _display_error_message(self, message: str):
        """Display an error message"""
        self.question_label.setPixmap(QPixmap())
        self.question_label.setText(f"Error: {message}")
        self._clear_options()
        
    def get_selected_option(self) -> Optional[str]:
        """Get the currently selected option letter"""
        try:
            checked_button = self.button_group.checkedButton()
            if checked_button:
                return getattr(checked_button, 'option_letter', None)
            return None
        except Exception as e:
            logger.error(f"Error getting selected option: {e}")
            return None
            
    def clear_selection(self):
        """Clear the current selection"""
        try:
            checked_button = self.button_group.checkedButton()
            if checked_button:
                checked_button.setChecked(False)
            self.explanation_container.hide()
        except Exception as e:
            logger.error(f"Error clearing selection: {e}")
