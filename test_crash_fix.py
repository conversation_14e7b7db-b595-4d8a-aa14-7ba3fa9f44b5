#!/usr/bin/env python3
"""
Test script to verify the crash fix implementation.

This script tests the CPU offload strategy to ensure it prevents system crashes
when loading large models on RTX 3060 12GB.
"""

import sys
import os
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_cpu_offload_strategy():
    """Test the CPU offload strategy for stable model loading."""
    try:
        logger.info("🧪 Testing CPU offload strategy for crash prevention")
        
        # Import the fixed offline MCQ generator
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        # Create generator instance
        generator = OfflineMCQGenerator()
        
        # Test initialization with CPU offload strategy
        logger.info("🔄 Testing initialization with CPU offload strategy...")
        success = generator.initialize()
        
        if success:
            logger.info("✅ CPU offload strategy initialization successful!")
            
            # Test a simple MCQ generation
            test_content = """
            Machine learning is a subset of artificial intelligence that focuses on 
            algorithms that can learn from and make predictions on data. It involves 
            training models on datasets to recognize patterns and make decisions 
            without being explicitly programmed for every scenario.
            """
            
            logger.info("🧠 Testing MCQ generation with CPU offload strategy...")
            result = generator.generate_question_sync(test_content, difficulty="medium")
            
            if result and 'question' in result:
                logger.info("✅ MCQ generation successful!")
                logger.info(f"📝 Generated question: {result['question'][:100]}...")
                return True
            else:
                logger.warning("⚠️ MCQ generation returned empty result")
                return False
                
        else:
            logger.error("❌ CPU offload strategy initialization failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False

def test_gguf_availability():
    """Test if GGUF engine is available and ready."""
    try:
        logger.info("🧪 Testing GGUF engine availability")
        
        from knowledge_app.core.gguf_model_inference import GGUFModelInference
        
        # Test with a hypothetical GGUF model path
        test_path = "models/gguf_models/mistral-7b-instruct-v0.2.Q4_K_M.gguf"
        engine = GGUFModelInference(test_path)
        
        if engine.is_available():
            logger.info("✅ GGUF engine is available and model file exists!")
            
            # Test loading
            if engine.load_model():
                logger.info("✅ GGUF model loaded successfully!")
                
                # Test generation
                test_response = engine.generate_text("What is machine learning?", max_tokens=50)
                logger.info(f"✅ GGUF generation test: {test_response[:100]}...")
                
                engine.unload_model()
                return True
            else:
                logger.warning("⚠️ GGUF model failed to load")
                return False
        else:
            logger.info("ℹ️ GGUF engine not available (model file not found or llama-cpp-python not installed)")
            return False
            
    except ImportError:
        logger.info("ℹ️ GGUF engine not available (llama-cpp-python not installed)")
        return False
    except Exception as e:
        logger.warning(f"⚠️ GGUF test failed: {e}")
        return False

def main():
    """Run all crash fix tests."""
    logger.info("🚀 Starting crash fix verification tests")
    logger.info("=" * 60)
    
    # Test 1: CPU Offload Strategy
    logger.info("TEST 1: CPU Offload Strategy")
    logger.info("-" * 30)
    cpu_offload_success = test_cpu_offload_strategy()
    
    logger.info("")
    
    # Test 2: GGUF Availability
    logger.info("TEST 2: GGUF Engine Availability")
    logger.info("-" * 30)
    gguf_success = test_gguf_availability()
    
    logger.info("")
    logger.info("=" * 60)
    logger.info("🏁 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    if cpu_offload_success:
        logger.info("✅ CPU Offload Strategy: WORKING - Your system is now crash-resistant!")
    else:
        logger.error("❌ CPU Offload Strategy: FAILED - May need further debugging")
    
    if gguf_success:
        logger.info("✅ GGUF Engine: AVAILABLE - Maximum performance ready!")
    else:
        logger.info("ℹ️ GGUF Engine: NOT AVAILABLE - Download GGUF models for optimal performance")
    
    logger.info("")
    
    if cpu_offload_success:
        logger.info("🎉 SUCCESS: Your system should no longer crash when loading models!")
        logger.info("💡 Next steps:")
        logger.info("   1. Test your normal MCQ generation workflow")
        logger.info("   2. Download GGUF models for even better performance")
        logger.info("   3. Install llama-cpp-python when build tools are available")
    else:
        logger.error("⚠️ ATTENTION: CPU offload strategy test failed")
        logger.error("   Please check the error messages above and report the issue")
    
    return cpu_offload_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
