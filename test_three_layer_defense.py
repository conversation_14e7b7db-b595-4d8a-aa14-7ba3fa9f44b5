#!/usr/bin/env python3
"""
Test script for the Three-Layered Defense Against Mediocrity

This script tests the implementation of:
1. Layer 1: RAG Pipeline Validation (minimum 200 characters)
2. Layer 2: Intelligent Fallback with Curated Content
3. Layer 3: Enhanced Inquisitor's Mandate with Negative Constraints

Usage: python test_three_layer_defense.py
"""

import asyncio
import logging
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_layer_1_validation():
    """Test Layer 1: RAG Pipeline Validation"""
    print("\n" + "="*60)
    print("🛡️ TESTING LAYER 1: RAG PIPELINE VALIDATION")
    print("="*60)
    
    try:
        from knowledge_app.core.mcq_manager import MCQManager
        
        # Initialize MCQ Manager
        mcq_manager = MCQManager()
        
        # Test with a topic that should trigger validation
        test_topic = "magnetism"
        
        print(f"📝 Testing content retrieval for topic: '{test_topic}'")
        
        # Test the _retrieve_pure_content method directly
        content = await mcq_manager._retrieve_pure_content(test_topic)
        
        if content:
            print(f"✅ Content retrieved: {len(content)} characters")
            if len(content) >= 200:
                print("✅ Layer 1 validation PASSED - Content meets minimum threshold")
            else:
                print("⚠️ Layer 1 validation FAILED - Content below 200 characters")
        else:
            print("⚠️ No content retrieved - should trigger Layer 2 fallback")
            
        return content is not None and len(content) >= 200
        
    except Exception as e:
        print(f"❌ Layer 1 test failed: {e}")
        return False

async def test_layer_2_fallback():
    """Test Layer 2: Intelligent Fallback with Curated Content"""
    print("\n" + "="*60)
    print("🛡️ TESTING LAYER 2: INTELLIGENT FALLBACK")
    print("="*60)
    
    try:
        from knowledge_app.core.mcq_manager import MCQManager
        
        # Initialize MCQ Manager
        mcq_manager = MCQManager()
        
        # Test curated fallback content
        test_topics = ["magnetism", "physics", "chemistry", "programming", "unknown_topic"]
        
        for topic in test_topics:
            print(f"\n📝 Testing curated content for: '{topic}'")
            
            curated_content = mcq_manager._get_curated_fallback_content(topic)
            
            if curated_content:
                print(f"✅ Curated content found: {len(curated_content)} characters")
                if len(curated_content) >= 200:
                    print("✅ Layer 2 content meets quality threshold")
                else:
                    print("⚠️ Layer 2 content below quality threshold")
            else:
                print("❌ No curated content found")
        
        return True
        
    except Exception as e:
        print(f"❌ Layer 2 test failed: {e}")
        return False

async def test_layer_3_prompt_constraints():
    """Test Layer 3: Enhanced Inquisitor's Mandate"""
    print("\n" + "="*60)
    print("🛡️ TESTING LAYER 3: ENHANCED INQUISITOR'S MANDATE")
    print("="*60)
    
    try:
        from knowledge_app.core.advanced_rag_mcq_generator import AdvancedRAGMCQGenerator, MCQGenerationContext, CognitiveLevel
        
        # Initialize Advanced RAG MCQ Generator
        generator = AdvancedRAGMCQGenerator()
        
        # Create test context
        test_context = MCQGenerationContext(
            topic="magnetism",
            difficulty="medium",
            cognitive_level=CognitiveLevel.UNDERSTANDING,
            context_passages=["Test content about magnetism and magnetic fields"],
            source_documents=["test_doc.pdf"],
            target_audience="undergraduate university student"
        )
        
        # Test the prompt creation
        combined_context = "Magnetism is a physical phenomenon produced by the motion of electric charge."
        prompt = generator._create_inquisitor_prompt(test_context, combined_context)
        
        print("📝 Generated Inquisitor's Mandate prompt:")
        print("-" * 40)
        
        # Check for negative constraints
        negative_constraints_present = "NEGATIVE CONSTRAINTS" in prompt
        definition_warning = "Definition questions" in prompt or "What is" in prompt
        meta_level_warning = "Meta-level questions" in prompt
        
        print(f"✅ Negative constraints section present: {negative_constraints_present}")
        print(f"✅ Definition question warning: {definition_warning}")
        print(f"✅ Meta-level question warning: {meta_level_warning}")
        
        if negative_constraints_present and definition_warning and meta_level_warning:
            print("✅ Layer 3 prompt constraints PASSED")
            return True
        else:
            print("⚠️ Layer 3 prompt constraints INCOMPLETE")
            return False
        
    except Exception as e:
        print(f"❌ Layer 3 test failed: {e}")
        return False

async def test_end_to_end_generation():
    """Test end-to-end MCQ generation with all three layers"""
    print("\n" + "="*60)
    print("🎯 TESTING END-TO-END MCQ GENERATION")
    print("="*60)
    
    try:
        from knowledge_app.core.mcq_manager import MCQManager
        
        # Initialize MCQ Manager
        mcq_manager = MCQManager()
        
        # Test quiz generation
        quiz_params = {
            'topic': 'magnetism',
            'difficulty': 'medium',
            'cognitive_level': 'understanding'
        }
        
        print(f"📝 Generating MCQ for: {quiz_params}")
        
        result = await mcq_manager.generate_quiz_async(quiz_params)
        
        if result and result.get('question'):
            print("✅ MCQ generation successful!")
            print(f"Question: {result['question']}")
            print(f"Options: {result.get('options', {})}")
            print(f"Correct: {result.get('correct', 'Unknown')}")
            
            # Check if it's not a meta-level question
            question = result['question'].lower()
            is_meta_question = any(phrase in question for phrase in [
                "what is magnetism",
                "what is a key concept",
                "what is an important",
                "what is a principle"
            ])
            
            if not is_meta_question:
                print("✅ Question avoids meta-level patterns")
                return True
            else:
                print("⚠️ Question appears to be meta-level")
                return False
        else:
            print("❌ MCQ generation failed")
            return False
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 STARTING THREE-LAYERED DEFENSE TESTING")
    print("="*60)
    
    # Run all tests
    test_results = []
    
    test_results.append(await test_layer_1_validation())
    test_results.append(await test_layer_2_fallback())
    test_results.append(await test_layer_3_prompt_constraints())
    test_results.append(await test_end_to_end_generation())
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - Three-Layered Defense is working!")
    else:
        print("⚠️ Some tests failed - Review implementation")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
