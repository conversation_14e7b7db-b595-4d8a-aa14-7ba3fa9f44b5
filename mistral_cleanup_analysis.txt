MISTRAL MODEL DUPLICATE ANALYSIS REPORT
=======================================
Generated: 2025-06-13

TOTAL MISTRAL STORAGE USAGE: ~44 GB

DETAILED BREAKDOWN:
==================

1. HUGGINGFACE CACHE MODELS:
   ✅ KEEP: Mistral-7B-Instruct-v0.2 (Complete)
      Location: C:\Users\<USER>\.cache\huggingface\hub\models--mistralai--Mistral-7B-Instruct-v0.2\
      Size: 13.49 GB
      Status: Complete and working - THIS IS YOUR MAIN MODEL
      
   ❌ DELETE: Mistral-7B-v0.1 (Incomplete)
      Location: C:\Users\<USER>\.cache\huggingface\hub\models--mistralai--Mistral-7B-v0.1\
      Size: ~5 GB
      Status: Incomplete download with .incomplete files
      Reason: You have the better v0.2 version already

2. LM STUDIO GGUF MODELS:
   🔄 OPTIONAL DELETE: OpenHermes-2.5-Mistral-7B
      Location: C:\Users\<USER>\.cache\lm-studio\models\TheBloke\OpenHermes-2.5-Mistral-7B-GGUF\
      Size: 7.17 GB
      Status: Complete GGUF format
      Note: Delete only if you don't use LM Studio
      
   🔄 OPTIONAL DELETE: Nous-Hermes-2-Mistral-7B-DPO
      Location: C:\Users\<USER>\.cache\lm-studio\models\NousResearch\Nous-Hermes-2-Mistral-7B-DPO-GGUF\
      Size: 7.17 GB
      Status: Complete GGUF format
      Note: Delete only if you don't use LM Studio
      
   🔄 OPTIONAL DELETE: Moistral-11B-v3
      Location: C:\Users\<USER>\.cache\lm-studio\models\TheDrummer\Moistral-11B-v3\
      Size: 10.62 GB
      Status: Complete GGUF format
      Note: Delete only if you don't use LM Studio

3. LORA ADAPTERS (KEEP ALL):
   ✅ KEEP: Real 7B LoRA Adapter
      Location: C:\shared folder\knowledge_app\data\lora_adapters_mistral\real_7b\
      Size: 640 MB
      Status: Your trained adapter - IMPORTANT!
      
   ✅ KEEP: Training Checkpoints
      Location: C:\shared folder\knowledge_app\data\lora_adapters_mistral\real_7b\checkpoints\
      Size: 26 MB
      Status: Training checkpoint - KEEP FOR BACKUP

CLEANUP RECOMMENDATIONS:
========================

IMMEDIATE SAFE CLEANUP (Save ~5 GB):
- Delete incomplete Mistral-7B-v0.1 download
- Command: Remove-Item "C:\Users\<USER>\.cache\huggingface\hub\models--mistralai--Mistral-7B-v0.1" -Recurse -Force

OPTIONAL CLEANUP (Save additional ~25 GB):
- Delete LM Studio GGUF models if you only use HuggingFace models
- Only do this if you're sure you don't need LM Studio compatibility

FINAL OPTIMIZED SETUP:
======================
After cleanup, you would have:
- Mistral-7B-Instruct-v0.2 (13.49 GB) - Main model
- LoRA adapters (640 MB) - Your trained models
- Total: ~14 GB (down from 44 GB)

NOTES:
======
- Your main working model (Mistral-7B-Instruct-v0.2) is complete and working
- The Model Singleton fix will prevent reloading this model unnecessarily
- LoRA adapters are your custom trained models - never delete these
- LM Studio models are in GGUF format - different from HuggingFace safetensors
- Always backup important data before cleanup
