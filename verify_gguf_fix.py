#!/usr/bin/env python3
"""
GGUF Fix Verification Script

This script verifies that the GGUF engine implementation is working correctly
and that the old unstable transformers engine has been completely removed.
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def verify_clean_imports():
    """Verify that the OfflineMCQGenerator only imports GGUF-related modules."""
    try:
        # Read the file content
        with open("src/knowledge_app/core/offline_mcq_generator.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for old imports that should NOT be present
        forbidden_imports = [
            "from .model_singleton import",
            "from .real_7b_config import",
            "from .local_model_inference import",
            "import torch",
            "import transformers",
            "import bitsandbytes"
        ]
        
        for forbidden in forbidden_imports:
            if forbidden in content:
                logger.error(f"❌ Found forbidden import: {forbidden}")
                return False
        
        # Check for required imports that SHOULD be present
        required_imports = [
            "from .gguf_model_inference import GGUFModelInference"
        ]
        
        for required in required_imports:
            if required not in content:
                logger.error(f"❌ Missing required import: {required}")
                return False
        
        logger.info("✅ Import verification passed - only GGUF imports present")
        return True
        
    except Exception as e:
        logger.error(f"❌ Import verification failed: {e}")
        return False

def verify_no_fallback_code():
    """Verify that there's no fallback code to the old transformers engine."""
    try:
        # Read the file content
        with open("src/knowledge_app/core/offline_mcq_generator.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for forbidden references to old engine (excluding comments/docstrings)
        forbidden_references = [
            "self.local_inference",
            "load_in_4bit",
            "get_model",
            "ModelSingleton",
            "LocalModelConfig"
        ]

        for forbidden in forbidden_references:
            if forbidden in content:
                logger.error(f"❌ Found forbidden reference to old engine: {forbidden}")
                return False

        # Check for actual imports/usage (not just mentions in comments)
        forbidden_code = [
            "import transformers",
            "import bitsandbytes",
            "from transformers import",
            "from bitsandbytes import"
        ]

        for forbidden in forbidden_code:
            if forbidden in content:
                logger.error(f"❌ Found forbidden code: {forbidden}")
                return False
        
        logger.info("✅ No fallback code verification passed - old engine completely removed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Fallback code verification failed: {e}")
        return False

def verify_gguf_only_logic():
    """Verify that the logic only uses GGUF engine."""
    try:
        # Read the file content
        with open("src/knowledge_app/core/offline_mcq_generator.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for GGUF-specific code
        required_gguf_code = [
            "self.gguf_engine",
            "GGUFModelInference",
            "GGUF-Only",
            "stable GGUF engine"
        ]
        
        for required in required_gguf_code:
            if required not in content:
                logger.error(f"❌ Missing required GGUF code: {required}")
                return False
        
        logger.info("✅ GGUF-only logic verification passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ GGUF logic verification failed: {e}")
        return False

def verify_file_structure():
    """Verify that all required files are present."""
    required_files = [
        "src/knowledge_app/core/gguf_model_inference.py",
        "src/knowledge_app/core/offline_mcq_generator.py",
        "config/gguf_config.json",
        "docs/gguf_setup_guide.md",
        "install_gguf_engine.py"
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            logger.error(f"❌ Missing required file: {file_path}")
            return False
    
    logger.info("✅ File structure verification passed")
    return True

def verify_no_crash_triggers():
    """Verify that known crash triggers have been removed."""
    try:
        # Read the file content
        with open("src/knowledge_app/core/offline_mcq_generator.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check for crash triggers that should NOT be present (actual code, not comments)
        crash_triggers = [
            "Loading checkpoint shards",
            "load_in_4bit=True",
            "BitsAndBytesConfig",
            "torch.cuda.empty_cache()",
            "device_map"
        ]

        for trigger in crash_triggers:
            if trigger in content:
                logger.error(f"❌ Found crash trigger: {trigger}")
                return False

        # Check for actual bitsandbytes usage (not just mentions in comments)
        bitsandbytes_code = [
            "import bitsandbytes",
            "from bitsandbytes import",
            "BitsAndBytesConfig"
        ]

        for trigger in bitsandbytes_code:
            if trigger in content:
                logger.error(f"❌ Found bitsandbytes code: {trigger}")
                return False
        
        logger.info("✅ No crash triggers found - all removed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Crash trigger verification failed: {e}")
        return False

def verify_clean_initialization():
    """Verify that initialization is clean and GGUF-only."""
    try:
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        # Create instance
        generator = OfflineMCQGenerator()
        
        # Verify it has GGUF engine attribute
        if not hasattr(generator, 'gguf_engine'):
            logger.error("❌ Generator missing gguf_engine attribute")
            return False
        
        # Verify it does NOT have old attributes
        forbidden_attributes = ['local_inference', 'model_lock', 'gguf_model_paths']
        for attr in forbidden_attributes:
            if hasattr(generator, attr):
                logger.error(f"❌ Generator has forbidden attribute: {attr}")
                return False
        
        logger.info("✅ Clean initialization verification passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Clean initialization verification failed: {e}")
        return False

def main():
    """Run all verification tests."""
    logger.info("🔍 GGUF Fix Verification")
    logger.info("=" * 50)
    
    tests = [
        ("Clean Imports", verify_clean_imports),
        ("No Fallback Code", verify_no_fallback_code),
        ("GGUF-Only Logic", verify_gguf_only_logic),
        ("File Structure", verify_file_structure),
        ("No Crash Triggers", verify_no_crash_triggers),
        ("Clean Initialization", verify_clean_initialization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Verifying: {test_name}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
    
    logger.info(f"\n📊 Verification Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("\n🎉 GGUF FIX VERIFICATION COMPLETE!")
        logger.info("✅ The old unstable transformers engine has been completely removed")
        logger.info("✅ Only the stable GGUF engine remains")
        logger.info("✅ No more crash triggers present")
        logger.info("✅ Clean, surgical implementation achieved")
        logger.info("\n🚀 The application will now ONLY use the stable GGUF engine")
        logger.info("🚀 No more 'Loading checkpoint shards' crashes!")
        logger.info("🚀 No more VRAM spikes!")
        logger.info("🚀 No more system freezes!")
        logger.info("\n📋 Next Steps:")
        logger.info("1. Run: python install_gguf_engine.py")
        logger.info("2. Download: mistral-7b-instruct-v0.2.Q5_K_M.gguf")
        logger.info("3. Place in: models/gguf_models/")
        logger.info("4. Start the app and look for: '🚀🚀🚀 OFFLINE GENERATOR READY (STABLE GGUF ENGINE) 🚀🚀🚀'")
        return 0
    else:
        logger.error("\n❌ VERIFICATION FAILED!")
        logger.error("Some issues remain. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
